"""
Handles the creation and management of quizzes,
saving user answers, generating quiz codes, and handling quiz results.

Classes:
- None

Functions:
- convert_to_binary: Converts a shell script into a binary executable.
- get_all_question_ids: Extracts question IDs from a list of final questions.
- get_questions_by_difficulty: Filters questions based on difficulty level.
- calculate_question_score: Calculates score for a question based on difficulty and result.
- get_performance_level: Determines the performance level based on total score.
- check_and_save_answer: Checks and saves the user's answer to a question.
- save_result_to_db: Saves the user's quiz answer results to the database.
- convert_json_to_csv: Converts JSON data into CSV format.
- create_quiz: Creates a new quiz based on the provided topic and quiz name.
- get_value: Retrieves question count and time from environment variables.
- get_or_create_user: Gets or creates a user record and returns the internal ID.
- get_or_create_assessment: Gets or creates an assessment record and returns the ID.
- get_or_create_session: Gets or creates a session for the user and assessment.
- main: Starts the Flask app and compiles quiz shell scripts into binary executables.
"""

import asyncio
import json
import logging
import os
import random
import re
import threading
import time
import uuid
from datetime import datetime
from string import Template
from typing import List, Optional, Union

import httpx
import psycopg2
import psycopg2.extras
from fastapi import Depends, FastAPI, HTTPException, Query, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.sessions import SessionMiddleware

from .api.middlewares.hashid_middleware import hash_ids_in_response
from .api.routes.auth_routes import setup_auth
from .config.db_config import DATABASE_CONFIG, get_connection_pool
from .config.env_validator import check_environment
from .models.db_manager import (
    assessment_report_by_topic,
    assessment_report_by_user,
    assessment_report_with_question_stats,
    cleanup_expired_paused_sessions,
    count_questions_for_skills,
    divide_number,
    fetch_attempted_question_ids,
    fetch_dynamic_questions_excluding_fixed,
    fetch_final_questions,
    fetch_questions_for_fixed_assessment,
    fetch_questions_for_skills,
    fetch_user_progress,
    get_assessment_by_id,
    get_assessment_description,
    get_assessment_questions_by_id,
    get_final_question_ids,
    get_pause_status,
    get_questions_for_check,
    get_session_and_assessment_details_by_code,
    get_skillwise_heatmap_data,
    insert_final_questions_db,
    insert_quiz_creation_logs,
    insert_user_data,
    pause_session,
    resume_session,
)
from .services.create_quiz_questions import load_yaml_prompt
from .services.llm_client import query_model
from .utils.api_response import (
    error_response,
    paginated_response,
    raise_http_exception,
    success_response,
)
from .utils.hashid_utils import (
    decode_assessment_id,
    decode_session_code,
    decode_skill_id,
)
from .utils.logger import (
    clear_context,
    debug,
    error,
    info,
    log_api_request,
    log_api_response,
    set_context,
    warning,
)

# Create FastAPI app with metadata
app = FastAPI(
    title="Quiz API",
    description="API for quiz management and assessment",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    # Specify the frontend origin explicitly
    allow_origins=os.getenv("FRONTEND_URL"),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Logging middleware
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Generate request ID
        request_id = str(uuid.uuid4())
        start_time = time.time()

        # Get user info from session if available
        user_id = None
        try:
            if hasattr(request, "session") and "session" in request.scope:
                user_info = request.session.get("user", {})
                user_id = user_info.get("sub") or user_info.get("email")
        except (AttributeError, AssertionError):
            # Session not available, continue without user_id
            pass

        # Log request
        log_api_request(
            method=request.method,
            endpoint=str(request.url.path),
            user_id=user_id,
            request_id=request_id,
            query_params=dict(request.query_params) if request.query_params else None,
        )

        # Set context for this request
        set_context(request_id=request_id, user_id=user_id)

        try:
            response = await call_next(request)

            # Calculate response time
            response_time = time.time() - start_time

            # Log response
            log_api_response(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=response.status_code,
                response_time=response_time,
                user_id=user_id,
                request_id=request_id,
            )

            return response

        except Exception as e:
            response_time = time.time() - start_time

            # Log error response
            log_api_response(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=500,
                response_time=response_time,
                user_id=user_id,
                request_id=request_id,
                error=str(e),
            )

            error("Unhandled exception in API endpoint", exception=e)
            raise
        finally:
            # Clear context after request
            clear_context()


# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Add session middleware (must be added after LoggingMiddleware to ensure proper order)
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("AUTH_CLIENT_SECRET"),
    # max_age=3600  # 1 hour session
)


# Background task for cleaning up expired paused sessions
def cleanup_task():
    """Background task that runs every 5 minutes to clean up expired paused sessions"""
    while True:
        try:
            expired_count = cleanup_expired_paused_sessions()
            if expired_count > 0:
                info(f"Background cleanup: expired {expired_count} paused sessions")
        except Exception as e:
            error("Error in background cleanup task", exception=e)

        # Sleep for 5 minutes
        time.sleep(300)


# Start background cleanup task
cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
cleanup_thread.start()
info("Started background cleanup task for expired paused sessions")


# Simple rate limiter
class RateLimiter:
    def __init__(self, requests_per_minute=60):
        self.requests_per_minute = requests_per_minute
        self.request_history = {}
        self.cleanup_interval = 60  # Cleanup old entries every minute
        self.last_cleanup = time.time()

    async def __call__(self, request: Request):
        # Clean up old entries
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            await self._cleanup(current_time)
            self.last_cleanup = current_time

        # Get client IP
        client_ip = request.client.host

        # Check if client has exceeded rate limit
        if client_ip in self.request_history:
            requests = self.request_history[client_ip]
            # Remove requests older than 1 minute
            requests = [t for t in requests if current_time - t < 60]

            if len(requests) >= self.requests_per_minute:
                raise HTTPException(
                    status_code=429, detail="Too many requests. Please try again later."
                )

            self.request_history[client_ip] = requests + [current_time]
        else:
            self.request_history[client_ip] = [current_time]

    async def _cleanup(self, current_time):
        """Remove entries older than 1 minute"""
        for ip in list(self.request_history.keys()):
            self.request_history[ip] = [
                t for t in self.request_history[ip] if current_time - t < 60
            ]
            if not self.request_history[ip]:
                del self.request_history[ip]


# Initialize rate limiter
rate_limiter = RateLimiter()

# Load allowed users from the environment variable
ALLOWED_USERS = [
    user.strip() for user in os.getenv("USERS", "").split(",") if user.strip()
]


# Define Pydantic models for request validation
class CreateQuizRequest(BaseModel):
    topic: str  # This is the user-defined description for the assessment
    quiz_name: str
    user_id: str
    skill_ids: List[int]  # Now properly used for many-to-many
    question_selection_mode: str = "dynamic"  # 'fixed' or 'dynamic'
    duration: int = 30  # Duration in minutes


class AnswerRequest(BaseModel):
    user_id: str
    question_id: str  # This is que_id from questions table
    answer: str
    session_code: str  # Changed from quiz_code
    time_taken: Optional[int] = None  # Time taken in seconds


class SessionCodeRequest(BaseModel):  # Renamed from QuizCodeRequest
    session_code: str


class StartSessionRequest(BaseModel):
    session_code: str


class SessionSubmissionRequest(BaseModel):
    session_code: str
    user_id: str


class UserCheckRequest(BaseModel):
    user_id: str


class SubQuizRequest(BaseModel):
    quiz_name: str


class SubQuizStatusRequest(BaseModel):
    quiz_name: str
    status: str


class FinalQuestionsRequest(BaseModel):
    question_ids: List[int]
    quiz_name: str
    assessment_id: Optional[int] = None  # For fixed assessments


class ReportRequest(BaseModel):
    report_type: str
    user_name: Optional[str] = None
    # This refers to assessment name or part of it
    report_topic: Optional[str] = None
    # For assessment-wise reports
    assessment_base_name: Optional[str] = None
    quiz_type: Optional[str] = None


class GenerateSkillQuestionsRequest(BaseModel):
    skillId: Union[int, str]  # Allow both integer and string IDs
    skillName: str
    skillDescription: str


class SuggestSkillDescriptionRequest(BaseModel):
    skill_name: str
    existing_description: Optional[str] = None


class GenerateSessionsRequest(BaseModel):
    assessment_id: int
    user_ids: List[str]


def get_questions_by_difficulty(all_questions, difficulty):
    """Filter questions based on the specified difficulty level."""
    return [q for q in all_questions if q["level"] == difficulty]


def calculate_question_score(level, result):
    """Calculate score for a single question based on difficulty level and result."""
    if result in ("timeout", "incorrect"):
        return 0

    score_mapping = {"easy": 1, "intermediate": 2, "advanced": 3}
    return score_mapping.get(level, 0)


def get_performance_level(total_score):
    """Determine performance level based on total score"""
    # Use questions_logging.get_performance_level which takes obtained and total
    # This is a fallback implementation

    # Get question counts for each difficulty level from environment variables
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

    # Max score calculation: easy(1) + intermediate(2) + advanced(3) points per question
    max_score = (easy_count * 1) + (intermediate_count * 2) + (advanced_count * 3)

    # Calculate percentage
    percentage = (total_score / max_score * 100) if max_score > 0 else 0

    if total_score == 0:
        return "Fail"
    if percentage < 33:
        return "Basic"
    if percentage < 62:
        return "Acceptable"
    if percentage < 85:
        return "Exceed Expectation"
    return "OUTSTANDING"


def decode_session_code_input(session_code_input: str) -> str:
    """
    Decode session code input - handles both raw 6-digit codes and encoded hashes.

    Args:
        session_code_input: Either a 6-digit session code or an encoded hash

    Returns:
        6-digit session code string

    Raises:
        HTTPException: If session code is invalid
    """
    debug(f"Decoding session code input: '{session_code_input}' (type: {type(session_code_input)}, len: {len(session_code_input) if session_code_input else 'None'})")
    
    # Handle None or empty string
    if not session_code_input:
        error("Empty session code provided")
        raise HTTPException(
            status_code=400,
            detail="Session code cannot be empty",
        )
    
    # Ensure session_code_input is a string
    session_code_input = str(session_code_input)
    
    # Check if it's already a 6-digit code
    if session_code_input.isdigit() and len(session_code_input) == 6:
        debug(f"Session code is already 6-digit: {session_code_input}")
        return session_code_input

    # Try to decode as hash
    decoded_code = decode_session_code(session_code_input)
    debug(f"Decoded hash '{session_code_input}' to: {decoded_code}")
    if decoded_code:
        return decoded_code

    # If neither worked, raise an error
    error(f"Failed to decode session code: '{session_code_input}' - not a 6-digit code and hash decode failed")
    raise HTTPException(
        status_code=400,
        detail="Invalid session code format. Must be either a 6-digit code or valid hash.",
    )


def check_and_save_answer(
    user_id: str,
    question_id: str,
    answer: str,
    session_code: str,
    time_taken: Optional[int] = None,
):
    """
    Check the given answer and save the result to the database.
    This function is called internally by the endpoint.
    """
    try:

        # Get session details
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            raise HTTPException(
                status_code=404, detail={"error": "Invalid session code"}
            )

        # Always get the correct user_id from the session
        session_user_id = None
        try:
            # Get the external_id (username) for the user associated with this session
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """
                        SELECT u.external_id
                        FROM users u
                        WHERE u.id = %s
                        """,
                        (session_details["user_id"],),
                    )
                    user_result = cur.fetchone()
                    if user_result:
                        session_user_id = user_result["external_id"]

                    else:

                        raise HTTPException(
                            status_code=404,
                            detail={"error": "User not found for this session"},
                        )
        except Exception as e:
            error("Error fetching user for session", exception=e)
            raise HTTPException(
                status_code=500,
                detail={"error": f"Error fetching user for session: {str(e)}"},
            )

        # Always use the user_id from the session
        if session_user_id:
            if session_user_id != user_id:
                warning(
                    f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}"
                )
            # Always use the session's user_id
            user_id = session_user_id

        assessment_name = session_details["assessment_name"]
        # The 'topic' in questions table is effectively the assessment_name_timestamp
        # We need to ensure `get_questions_for_check` uses the correct identifier.
        # Let's assume assessment_name is the correct 'topic' for questions.
        # This might need refinement if `questions.topic` is `quiz_nm_timestamp`.
        # For now, let's assume `assessment_name` is the `quiz_name` argument for `get_questions_for_check`.

        # The `assessment_name` from `sessions` table is like "UserAssessmentName_timestamp Assessment"
        # The `topic` in `questions` table is like "UserAssessmentName_timestamp"
        # We need to extract the base name.
        question_topic_identifier = assessment_name.replace(" Assessment", "")

        # For skill-based assessments, we need to look up questions by ID only
        # since questions can come from different topics/skills
        question = None

        # First try the original method (for backward compatibility)
        question = get_questions_for_check(question_topic_identifier, question_id)

        # If not found and this is a skill-based assessment, try looking up by question_id only
        if not question:
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                        cur.execute(
                            """
                            SELECT que_id, question, options, answer, level, topic
                            FROM questions
                            WHERE que_id = %s
                            """,
                            (int(question_id),),
                        )
                        row = cur.fetchone()
                        if row:
                            question = dict(row)
            except Exception as e:
                error("Error looking up question by ID", exception=e)

        if not question:
            raise HTTPException(status_code=404, detail={"error": "Question not found"})

        correct_answer_key = question["answer"]
        correct_answer_value = question["options"].get(correct_answer_key, "")
        is_correct = answer.lower() == correct_answer_key.lower()

        quiz_type = "assessment"  # Always use 'assessment' instead of 'mock' or 'final'

        save_result_to_db(
            user_id=user_id,
            # que_id from the fetched question
            question_id_int=question["que_id"],
            answer=answer,
            is_correct=is_correct,
            session_code=session_code,
            time_taken=time_taken,
            # Pass derived assessment_name and quiz_type for legacy user_assessment table
            legacy_topic=question_topic_identifier,
            legacy_quiz_type=quiz_type,
            question_level=question["level"],
            question_text=str(question["question"]),
            question_options_json=json.dumps(question["options"]),
            question_correct_answer_key=correct_answer_key,
        )

        # Get updated session details to return current score and remaining time
        updated_session_details = get_session_and_assessment_details_by_code(
            session_code
        )
        current_score = 0
        remaining_time_seconds = 0
        attempted_questions_count = 0

        if updated_session_details:
            # Calculate current score from user_answers
            attempted_questions = updated_session_details.get("attempted_questions", [])
            attempted_questions_count = len(attempted_questions)

            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        cur.execute(
                            """SELECT SUM(score) as total_score
                               FROM user_answers
                               WHERE session_id = %s""",
                            (updated_session_details["session_id"],),
                        )
                        score_result = cur.fetchone()
                        if score_result and score_result[0]:
                            current_score = float(score_result[0])
            except Exception as e:
                error("Error calculating current score", exception=e)

            # Get remaining time
            remaining_time_seconds = updated_session_details.get(
                "remaining_time_seconds", 0
            )

        # Get assessment details including question_selection_mode
        question_selection_mode = "dynamic"  # Default to dynamic
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        "SELECT question_selection_mode FROM assessments WHERE id = %s",
                        (session_details["assessment_id"],),
                    )
                    assessment_details = cur.fetchone()
                    if assessment_details and assessment_details["question_selection_mode"]:
                        question_selection_mode = assessment_details["question_selection_mode"]
        except Exception as e:
            error(f"Error fetching assessment details in check_and_save_answer: {e}")
            # Continue with default value if there's an error

        response_data = {
            "is_correct": is_correct,
            "correct_answer_key": correct_answer_key,
            "correct_answer_value": correct_answer_value,
            "question_id": question["que_id"],  # Return the actual que_id
            "quiz_type": quiz_type,  # always 'assessment'
            "current_score": current_score,
            "remaining_time_seconds": remaining_time_seconds,
            "attempted_questions_count": attempted_questions_count,
            "question_selection_mode": question_selection_mode,
        }

        return success_response(
            data=response_data, message="Answer checked and saved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error("Error in check_and_save_answer_internal", exception=e)
        raise HTTPException(status_code=500, detail={"error": str(e)})


def save_result_to_db(
    user_id: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    session_code: str,
    time_taken: Optional[int],
    legacy_topic: str,
    legacy_quiz_type: str,
    question_level: str,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
):
    """Save quiz answer results to the database using the new schema."""

    lowercased_answer = answer.lower()
    if lowercased_answer == "timeout":
        result_status = "Timeout"  # For legacy table
        # is_correct_for_score = False
    elif is_correct:
        result_status = "Correct"
    else:
        result_status = "Incorrect"
        # is_correct_for_score = False

    # Use is_correct_for_score for score calculation, is_correct for user_answers.is_correct
    score = calculate_question_score(question_level, result_status.lower())

    try:
        # For backward compatibility, insert into user_assessment
        insert_user_data(
            {
                "user_id": user_id,
                "topic": legacy_topic,  # This is assessment_name_timestamp
                "level": question_level,
                "quiz_type": legacy_quiz_type,  # mock or final
                "que_id": question_id_int,
                "question": question_text,
                "options": question_options_json,  # Already a JSON string
                "correct_answer": question_correct_answer_key,
                "user_answer": "None" if answer.lower() == "timeout" else answer,
                "result": result_status,
                "score": score,
            }
        )

        # Get session details
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            # This should ideally not happen if check_and_save_answer validated it
            raise ValueError(
                f"Session code {session_code} not found during save_result_to_db"
            )

        internal_session_id = session_details["session_id"]

        # Insert into user_answers table
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """INSERT INTO user_answers
                       (session_id, question_id, user_answer, is_correct, score, time_taken, created_at)
                       VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                       ON CONFLICT (session_id, question_id)
                       DO UPDATE SET user_answer = EXCLUDED.user_answer,
                                     is_correct = EXCLUDED.is_correct,
                                     score = EXCLUDED.score,
                                     time_taken = EXCLUDED.time_taken""",
                    (
                        internal_session_id,
                        question_id_int,
                        answer,
                        is_correct,
                        score,
                        time_taken,
                    ),
                )
                conn.commit()

        return success_response(
            data=None,
            message="Answer submitted successfully",
        )
    except Exception as e:
        error("Error saving result", exception=e)
        return error_response(
            message=f"Error saving result: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


async def apply_migrations():
    """Apply database migrations from the migrations directory"""
    info("Checking for database migrations to apply...")

    try:
        import os

        import psycopg2

        # Connect to the database
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create migrations table if it doesn't exist
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS migrations (
                        id SERIAL PRIMARY KEY,
                        name TEXT UNIQUE NOT NULL,
                        batch INTEGER NOT NULL,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Get list of applied migrations
                cur.execute("SELECT name FROM migrations")
                applied_migrations = {row[0] for row in cur.fetchall()}

                # Get list of migration files

                current_dir = os.path.dirname(os.path.abspath(__file__))
                # Get the absolute path of the project root (/app)
                project_root = os.path.dirname(current_dir)
                # Build the correct, absolute path to the migrations directory
                migrations_dir = os.path.join(project_root, "database", "migrations")

                if not os.path.exists(migrations_dir):
                    warning(f"Migrations directory '{migrations_dir}' not found")
                    return

                migration_files = sorted(
                    [f for f in os.listdir(migrations_dir) if f.endswith(".sql")]
                )

                # Apply new migrations
                applied_count = 0
                for migration_file in migration_files:
                    if migration_file not in applied_migrations:
                        info(f"Applying migration: {migration_file}")

                        # Read migration file
                        with open(
                            os.path.join(migrations_dir, migration_file), "r"
                        ) as f:
                            sql_content = f.read()

                        # Apply migration
                        cur.execute(sql_content)

                        # Get the current highest batch number
                        cur.execute("SELECT COALESCE(MAX(batch), 0) FROM migrations")
                        current_batch = cur.fetchone()[0]
                        next_batch = current_batch + 1

                        # Record migration as applied
                        cur.execute(
                            "INSERT INTO migrations (name, batch) VALUES (%s, %s)",
                            (migration_file, next_batch),
                        )

                        applied_count += 1

                conn.commit()

                if applied_count > 0:
                    info(f"Applied {applied_count} new migrations")
                else:
                    info("No new migrations to apply")

    except Exception as e:
        error("Failed to apply migrations", exception=e)
        raise


def get_or_create_user(user_id):
    """
    Get or create a user record and return the internal ID.
    If user_id is an email, extract the username part (before @) for external_id and display_name.
    First checks if a user with the same email exists, then checks by external_id.
    """
    try:
        # Check if input is an email and extract username if it is
        if "@" in user_id:
            email = user_id
            username = user_id.split("@")[0]  # Extract part before @
        else:
            email = None
            username = user_id

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First check if a user with this email exists (if email is provided)
                if email:
                    cur.execute(
                        "SELECT id, external_id FROM users WHERE email = %s", (email,)
                    )
                    result = cur.fetchone()
                    if result:
                        # Found user with this email - return their ID without changing external_id
                        # This avoids conflicts with the unique constraint on external_id
                        return result[0]

                # Then check if user exists by external_id
                cur.execute("SELECT id FROM users WHERE external_id = %s", (username,))
                result = cur.fetchone()
                if result:
                    # If we have an email but the user doesn't, update it
                    if email:
                        cur.execute(
                            "UPDATE users SET email = %s WHERE id = %s AND (email IS NULL OR email = '')",
                            (email, result[0]),
                        )
                        conn.commit()
                    return result[0]

                # Create new user with username as both external_id and display_name
                # For email users, use the full email as external_id to avoid conflicts
                external_id = user_id if "@" in user_id else username

                cur.execute(
                    "INSERT INTO users (external_id, email, display_name) VALUES (%s, %s, %s) RETURNING id",
                    (external_id, email, username),
                )
                user_internal_id = cur.fetchone()[0]
                conn.commit()
                return user_internal_id
    except Exception as e:
        error(f"Error in get_or_create_user: {e}")
        raise


def get_or_create_assessment(
    assessment_topic_name: str,
    is_final=False,
    description: Optional[str] = None,
    skill_ids: List[int] = [],
    question_selection_mode: str = "dynamic",
):
    """
    Create assessment with multiple skills.

    Args:
        assessment_topic_name: Base name for the assessment
        is_final: Whether this is a final assessment
        description: Optional description for the assessment
        skill_ids: List of skill IDs to associate with this assessment
        question_selection_mode: How questions are selected - 'fixed' or 'dynamic'

    Returns:
        The ID of the created or existing assessment
    """
    # Validate question_selection_mode
    if question_selection_mode not in ["fixed", "dynamic"]:
        raise ValueError("question_selection_mode must be either 'fixed' or 'dynamic'")

    try:
        # Get total questions count from environment variable
        total_questions = int(os.getenv("TOTAL_QUESTIONS_COUNT", "30"))

        full_assessment_name = (
            f"{assessment_topic_name} {'Final' if is_final else 'Mock'} Assessment"
        )
        assessment_description = (
            description
            or f"{'Final' if is_final else 'Mock'} assessment for {assessment_topic_name}"
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if assessment exists
                cur.execute(
                    "SELECT id FROM assessments WHERE name = %s",
                    (full_assessment_name,),
                )
                result = cur.fetchone()

                if result:
                    assessment_id = result[0]
                    # Ensure skill associations exist
                    for skill_id in skill_ids:
                        cur.execute(
                            """INSERT INTO assessment_skills
                            (assessment_id, skill_id)
                            VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                            (assessment_id, skill_id),
                        )
                    return assessment_id

                # Create new assessment with composition that matches total_questions
                # Default composition is {"basic": 6, "intermediate": 6, "advanced": 8} which adds up to 20
                # We need to adjust it to match total_questions (20)
                composition = {"basic": 6, "intermediate": 6, "advanced": 8}

                cur.execute(
                    """INSERT INTO assessments
                       (name, description, is_final, total_questions, question_selection_mode, composition)
                       VALUES (%s, %s, %s, %s, %s, %s) RETURNING id""",
                    (
                        full_assessment_name,
                        assessment_description,
                        is_final,
                        total_questions,
                        question_selection_mode,
                        json.dumps(composition),
                    ),
                )
                assessment_id = cur.fetchone()[0]

                # Create skill associations
                for skill_id in skill_ids:
                    cur.execute(
                        """INSERT INTO assessment_skills
                        (assessment_id, skill_id)
                        VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                        (assessment_id, skill_id),
                    )

                conn.commit()

                # Log the assessment creation for new assessments only
                # Note: This function doesn't have user_id, so we'll use a default admin user
                # In a real scenario, you might want to pass user_id as a parameter
                try:
                    insert_quiz_creation_logs(
                        [
                            {
                                "user_id": "system",  # Default user for system-created assessments
                                "assessment_name": full_assessment_name,
                                "assessment_description": assessment_description,
                                "total_questions": total_questions,
                                "assessment_id": assessment_id,
                            }
                        ]
                    )
                except Exception as log_error:
                    # Don't fail the assessment creation if logging fails
                    warning(f"Failed to log assessment creation: {log_error}")

                return assessment_id
    except Exception as e:
        error(
            f"Error in get_or_create_assessment for '{assessment_topic_name}': {e}",
            exc_info=True,
        )
        raise


def get_or_create_session(user_id_external: str, assessment_id: int, session_code: str):
    """
    Get or create a session for the user and assessment.
    If session exists by code, it updates its status to 'in_progress' if 'pending'.
    """
    try:
        internal_user_id = get_or_create_user(user_id_external)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if session exists for this code
                cur.execute(
                    """SELECT id, user_id, assessment_id FROM sessions
                       WHERE code = %s""",
                    (session_code,),
                )
                result = cur.fetchone()
                if result:
                    (
                        session_db_id,
                        session_user_id,
                        session_assessment_id,
                    ) = result
                    # If session exists, ensure it's for the correct user and assessment
                    # This logic might need adjustment based on whether sessions can be reused
                    # or are strictly user-assessmentspecific from creation
                    if (
                        session_user_id != internal_user_id
                        or session_assessment_id != assessment_id
                    ):
                        # This case implies a code collision or misuse, which should be rare for 6-digit codes
                        # if they are globally unique for active sessions.
                        # Or, if sessions are pre-generated for specific users, this is an auth check.
                        error(
                            f"Session code {session_code} mismatch: user ({session_user_id} vs {internal_user_id}) "
                            f"or assessment ({session_assessment_id} vs {assessment_id})"
                        )
                        raise HTTPException(
                            status_code=403,
                            detail="Session code mismatch or unauthorized.",
                        )

                    # Get assessment duration to calculate completed_at
                    # Get assessment duration for reference (not used in this context)
                    cur.execute(
                        """SELECT duration_minutes FROM assessments
                           WHERE id = %s""",
                        (assessment_id,),
                    )

                    # For resumed sessions, only set started_at if not already set
                    # Don't update completed_at as we calculate remaining time based on started_at
                    cur.execute(
                        """UPDATE sessions SET status = 'in_progress',
                           started_at = COALESCE(started_at, CURRENT_TIMESTAMP AT TIME ZONE 'UTC')
                           WHERE id = %s AND (status = 'pending' OR status = 'in_progress') RETURNING id""",
                        (session_db_id,),
                    )
                    updated_session = cur.fetchone()
                    conn.commit()
                    if updated_session:
                        return updated_session[0]
                    else:  # Session might be completed or expired
                        cur.execute(
                            "SELECT id FROM sessions WHERE id = %s", (session_db_id,)
                        )
                        return cur.fetchone()[
                            0
                        ]  # Return existing id anyway if status not pending/in_progress

                # If session code does not exist, this function should not create it.
                # Sessions are expected to be created by /generate_sessions.
                # A user attempting a quiz should use an existing session code.
                error(
                    f"Session code {session_code} not found. Cannot create on-the-fly in get_or_create_session."
                )
                return error_response(
                    message="Invalid session code.",
                    code=status.HTTP_404_NOT_FOUND,
                    error_type="NotFound",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error in get_or_create_session: {e}", exc_info=True)
        if conn:
            conn.rollback()
        return error_response(
            message="Error processing session.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


def expire_sessions():
    """
    Background task to mark sessions as expired if past completed_at and not completed.
    This function runs periodically to check for expired sessions.
    """
    try:
        info("Running session expiry check...")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Find sessions that are past their completed_at time and still in progress
                cur.execute(
                    """UPDATE sessions
                       SET status = 'expired'
                       WHERE status = 'in_progress'
                       AND completed_at IS NOT NULL
                       AND completed_at < CURRENT_TIMESTAMP
                       RETURNING id, code""",
                )
                expired_sessions = cur.fetchall()

                if expired_sessions:
                    info(
                        f"Expired {len(expired_sessions)} sessions: {[s['code'] for s in expired_sessions]}"
                    )
                else:
                    debug("No sessions to expire")

                conn.commit()

    except Exception as e:
        error(f"Error during session expiry check: {str(e)}", exc_info=True)


async def periodic_session_expiry():
    """
    Periodic task to run session expiry checks every 5 minutes.
    """
    while True:
        try:
            expire_sessions()
            # Wait 5 minutes before next check
            await asyncio.sleep(300)
        except Exception as e:
            error(f"Error in periodic session expiry: {str(e)}")
            # Wait 1 minute before retrying on error
            await asyncio.sleep(60)


@app.on_event("startup")
async def startup_event():
    info("Starting server...")

    # Set up authentication
    setup_auth(app)
    info("Authentication setup complete")

    # Validate environment variables
    if not check_environment():
        error("Environment validation failed. Server may not function correctly.")

    # Start the periodic session expiry task
    asyncio.create_task(periodic_session_expiry())
    info("Started periodic session expiry task")

    get_connection_pool()
    info("Database connection pool initialized successfully.")


# =============================================================================
# ADMIN ENDPOINTS - Assessment Management
# =============================================================================


@app.post("/api/admin/quiz")
async def create_quiz(request: CreateQuizRequest, _: None = Depends(rate_limiter)):
    """
    Creates a new assessment based on the provided description and quiz name.

    Args:
        request (CreateQuizRequest): The request body containing:
            topic (str): The user-defined description for the assessment.
            quiz_name (str): The user-defined name for this assessment.
            user_id (str): The ID of the admin creating the quiz.
            skill_ids (list[int]): List of skill IDs to associate with this assessment.

    Returns:
        JSONResponse: A JSON response containing the ID of the created assessment,
        and a success message.
        Admins should then use the /generate_sessions endpoint to create usable session codes.

    Raises:
        HTTPException:
            - 400: If the description is missing or no questions are found.
            - 500: If an error occurs during quiz creation.
    """
    assessment_description = (
        request.topic
    )  # This is the user-defined description for the assessment
    user_defined_assessment_name = request.quiz_name
    user_id = request.user_id
    skill_ids = request.skill_ids

    if not assessment_description or len(assessment_description) < 20:
        raise HTTPException(
            status_code=400,
            detail="Valid assessment description (min 20 chars) is required",
        )
    if not user_defined_assessment_name or len(user_defined_assessment_name) < 3:
        raise HTTPException(
            status_code=400, detail="Quiz name must be at least 3 characters"
        )

    # Validate skill IDs before proceeding
    if not request.skill_ids:
        raise HTTPException(
            status_code=400,
            detail="At least one skill ID is required for quiz creation",
        )

    # Validate skill existence and description
    valid_skills = []
    for skill_id in request.skill_ids:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT id, description FROM skills WHERE id = %s", (skill_id,)
                )
                skill = cur.fetchone()
                if not skill or not skill[1] or len(skill[1]) < 20:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Skill {skill_id} not found or invalid description",
                    )
                valid_skills.append(skill)

    # Keep the user's description from the request.topic field
    # assessment_description already contains the user's description from request.topic

    # Get parameters from request early to avoid UnboundLocalError
    question_selection_mode = request.question_selection_mode
    duration = request.duration

    # Validate question selection mode
    if question_selection_mode not in ["fixed", "dynamic"]:
        raise HTTPException(
            status_code=400,
            detail="question_selection_mode must be either 'fixed' or 'dynamic'",
        )

    try:
        timestamp = datetime.now().strftime("%d_%m_%Y")
        # This combined name is used as the 'topic' in the questions table
        # and as the base for assessment names.
        assessment_base_name = f"{user_defined_assessment_name}_{timestamp}"

        # Skip generating questions with the model since we'll use existing questions from the selected skills
        # Instead, check if there are questions available for the selected skills
        # For dynamic assessments, exclude questions that are assigned to fixed assessments
        if question_selection_mode == "dynamic":
            # For dynamic assessments, exclude questions assigned to fixed assessments
            question_count = count_questions_for_skills(
                skill_ids, exclude_fixed_assessment_questions=True
            )
        else:
            # For fixed assessments, count all questions (they will be manually selected)
            question_count = count_questions_for_skills(
                skill_ids, exclude_fixed_assessment_questions=False
            )

        if question_count == 0:
            if question_selection_mode == "dynamic":
                raise HTTPException(
                    status_code=400,
                    detail="""No available questions found for the selected skills for dynamic assessment.
                    All questions may be assigned to fixed assessments.
                    Please select skills with available questions or create new questions.""",
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="""No questions found for the selected skills.
                    Please select skills with existing questions or create questions for these skills first.""",
                )

        info(f"Found {question_count} existing questions for the selected skills")

        # Since we're not generating questions, we don't need to create a CSV file
        # file_name = ""
        # csv_data = ""

        # Create a single assessment
        assessment_name = f"{assessment_base_name} Assessment"

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create the assessment
                cur.execute(
                    """
                    INSERT INTO assessments (
                        name, description, is_final, total_questions,
                        question_selection_mode, composition, duration_minutes
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (
                        assessment_name,
                        assessment_description,  # Use the user's description directly
                        False,  # is_final is no longer relevant but kept for DB compatibility
                        30,  # Default total_questions
                        question_selection_mode,
                        json.dumps({"basic": 10, "intermediate": 10, "advanced": 10}),
                        duration,
                    ),
                )
                assessment_id = cur.fetchone()[0]

                # Create skill associations - store all skill IDs as JSON in a single row
                if skill_ids:
                    # First, delete any existing skill associations for this assessment
                    cur.execute(
                        """DELETE FROM assessment_skills WHERE assessment_id = %s""",
                        (assessment_id,),
                    )
                    # Insert each skill ID individually
                    for skill_id in skill_ids:
                        cur.execute(
                            """INSERT INTO assessment_skills (assessment_id, skill_id)
                               VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                            (assessment_id, skill_id),
                        )

                conn.commit()

        # Log the assessment creation after successful creation
        insert_quiz_creation_logs(
            [
                {
                    "user_id": user_id,
                    "assessment_name": assessment_name,  # Use the full assessment name
                    # Use the user's description directly
                    "assessment_description": assessment_description,
                    "total_questions": question_count,  # Use the count of existing questions
                    "assessment_id": assessment_id,  # Include the assessment ID
                }
            ]
        )

        return success_response(
            data={
                "assessment_id": assessment_id,
                "assessment_base_name": assessment_base_name,
                "question_selection_mode": question_selection_mode,
                "skill_ids": skill_ids,
                "total_questions_available": question_count,
                "duration": duration,
            },
            message="Assessment created successfully. Please generate sessions to get usable codes.",
        )

    except Exception as e:
        error(f"Error creating quiz: {str(e)}", exc_info=True)
        raise_http_exception(
            status_code=500, detail=f"Error creating assessment: {str(e)}"
        )


@app.get("/api/get_value")
def get_value():
    """
    Retrieve assessment question count and time from environment variables.

    Returns:
        dict: A standardized response containing the total questions count and question time.
    """
    total_count = os.getenv("TOTAL_QUESTIONS_COUNT", "30")
    question_time = os.getenv("QUESTION_TIME", "0")

    return success_response(
        data={
            "total_questions_count": total_count,
            "question_time": question_time,
        },
        message="Configuration retrieved successfully",
    )


@app.post("/api/validate_session_code")
def validate_session_code(request: SessionCodeRequest):
    """
    Validates if a session code exists and returns basic session/assessment details.
    Does NOT update the session status.

    Args:
        request (SessionCodeRequest): The request body containing the session_code.

    Returns:
        dict: A dictionary containing basic session and assessment details if the code is valid.

    Raises:
        HTTPException:
            - 400: If the session code is missing or invalid format.
            - 404: If the session code doesn't exist.
            - 500: If there's a database error.
    """
    session_code_input = request.session_code

    if not session_code_input:
        raise HTTPException(status_code=400, detail="Session code is required.")

    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        session_code = decode_session_code_input(session_code_input)
        session_details = get_session_and_assessment_details_by_code(session_code)

        if not session_details:
            raise HTTPException(
                status_code=404, detail="Invalid or expired session code."
            )

        # Get started_at and completed_at for the response
        started_at = None
        completed_at = None

        if "started_at" in session_details and session_details["started_at"]:
            started_at = session_details["started_at"].isoformat()

        if "completed_at" in session_details and session_details["completed_at"]:
            completed_at = session_details["completed_at"].isoformat()

        # Get assessment details including question_selection_mode
        question_selection_mode = "dynamic"  # Default to dynamic
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        "SELECT question_selection_mode FROM assessments WHERE id = %s",
                        (session_details["assessment_id"],),
                    )
                    assessment_details = cur.fetchone()
                    if assessment_details and assessment_details["question_selection_mode"]:
                        question_selection_mode = assessment_details["question_selection_mode"]
        except Exception as e:
            error(f"Error fetching assessment details in validate_session_code: {e}")
            # Continue with default value if there's an error

        response_data = {
            "session_id": session_details["session_id"],
            # Add the original session code for users to see
            "session_code": request.session_code,
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            "session_status": session_details["session_status"],
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "attempted_questions_count": len(
                session_details.get("attempted_questions", [])
            ),
            "started_at": started_at,
            "completed_at": completed_at,
            "question_selection_mode": question_selection_mode,
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(
            data=hashed_data, message="Session code validated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error validating session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


@app.post("/api/start_session")
def start_session(request: StartSessionRequest):
    """
    Starts a session by updating its status to 'in_progress' and setting timestamps.

    Args:
        request (StartSessionRequest): The request body containing the session_code.

    Returns:
        dict: A dictionary containing updated session details.

    Raises:
        HTTPException:
            - 400: If the session code is missing or invalid format.
            - 404: If the session code doesn't exist.
            - 409: If the session is already in progress or completed.
            - 500: If there's a database error.
    """
    session_code_input = request.session_code

    if not session_code_input:
        raise HTTPException(status_code=400, detail="Session code is required.")

    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        session_code = decode_session_code_input(session_code_input)
        session_details = get_session_and_assessment_details_by_code(session_code)

        if not session_details:
            raise HTTPException(
                status_code=404, detail="Invalid or expired session code."
            )

        # Check if session can be started
        if session_details["session_status"] != "pending":
            raise HTTPException(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )

        # Update session status to 'in_progress'
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    # Get assessment duration for reference (not used in this context)
                    cur.execute(
                        """SELECT duration_minutes FROM assessments
                           WHERE id = %s""",
                        (session_details["assessment_id"],),
                    )

                    # Only set started_at when starting a new session
                    # Don't set completed_at as we calculate remaining time based on started_at
                    cur.execute(
                        """UPDATE sessions
                           SET status = 'in_progress',
                               started_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                           WHERE id = %s AND status = 'pending'""",
                        (session_details["session_id"],),
                    )
                    conn.commit()

                    # Refresh session details to get updated timestamps and status
                    session_details = get_session_and_assessment_details_by_code(
                        session_code
                    )

                    info(
                        f"Started session {session_code} with {session_details.get('remaining_time_seconds', 0)} "
                        f"seconds remaining"
                    )
        except Exception as e:
            error(f"Error updating session status for code {session_code}: {e}")
            raise HTTPException(
                status_code=500, detail=f"Failed to start session: {str(e)}"
            )

        # Get started_at and completed_at for the response
        started_at = None
        completed_at = None

        if "started_at" in session_details and session_details["started_at"]:
            started_at = session_details["started_at"].isoformat()

        if "completed_at" in session_details and session_details["completed_at"]:
            completed_at = session_details["completed_at"].isoformat()

        response_data = {
            "session_id": session_details["session_id"],
            "session_code": session_code,  # Add the original session code for users to see
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],

            "session_status": session_details["session_status"],
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "attempted_questions_count": len(
                session_details.get("attempted_questions", [])
            ),
            "started_at": started_at,
            "completed_at": completed_at,
        }

        info(
            f"Session {session_code} has {response_data['remaining_time_seconds']} seconds remaining"
        )
        hashed_data = hash_ids_in_response(response_data)
        return success_response(
            data=hashed_data, message="Session started successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error starting session {session_code}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")


@app.post("/api/pause_session")
async def pause_session_endpoint(request: Request):
    """
    Pause a session when user leaves/becomes inactive
    Handles both JSON and sendBeacon (text/plain) requests
    """
    try:
        # Handle different content types
        content_type = request.headers.get("content-type", "")

        if "application/json" in content_type:
            # Standard JSON request
            body = await request.json()
            session_code = decode_session_code_input(body.get("session_code"))
        elif "text/plain" in content_type or not content_type:
            # sendBeacon request (text/plain) or no content-type
            body_text = await request.body()
            try:
                # Try to parse as JSON
                import json

                body = json.loads(body_text.decode("utf-8"))
                session_code = decode_session_code_input(body.get("session_code"))
            except (json.JSONDecodeError, UnicodeDecodeError):
                # If not JSON, treat as plain session code
                session_code = decode_session_code_input(
                    body_text.decode("utf-8").strip()
                )
        else:
            raise HTTPException(status_code=400, detail="Unsupported content type")

        success = pause_session(session_code)

        if success:
            return success_response(
                data={"session_code": session_code, "paused": True},
                message="Session paused successfully",
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="Could not pause session - session not found or not in progress",
            )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error pausing session: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")


@app.post("/api/resume_session")
async def resume_session_endpoint(request: Request):
    """
    Resume a paused session when user returns/becomes active
    """
    try:
        # Handle different content types
        content_type = request.headers.get("content-type", "")
        debug(f"Resume session request with content-type: {content_type}")

        if "application/json" in content_type:
            # Standard JSON request
            body = await request.json()
            debug(f"Resume session JSON body: {body}")
            
            # Check if session_code exists in the body
            if "session_code" not in body:
                error("Missing session_code in request body")
                raise HTTPException(
                    status_code=400,
                    detail="Missing required field: session_code"
                )
                
            session_code = str(body.get("session_code"))
            session_code = decode_session_code_input(session_code)
        elif "text/plain" in content_type or not content_type:
            # sendBeacon request (text/plain) or no content-type
            body_text = await request.body()
            debug(f"Resume session text body: {body_text}")
            
            try:
                # Try to parse as JSON
                import json

                body = json.loads(body_text.decode("utf-8"))
                if "session_code" not in body:
                    error("Missing session_code in request body")
                    raise HTTPException(
                        status_code=400,
                        detail="Missing required field: session_code"
                    )
                    
                session_code = str(body.get("session_code"))
                session_code = decode_session_code_input(session_code)
            except (json.JSONDecodeError, UnicodeDecodeError):
                # If not JSON, treat as plain session code
                session_code = decode_session_code_input(
                    body_text.decode("utf-8").strip()
                )
        else:
            raise HTTPException(status_code=400, detail="Unsupported content type")

        result = resume_session(session_code)

        # Handle the case where session expired due to pause timeout
        if isinstance(result, dict) and result.get("expired"):
            return error_response(
                message="Session expired because pause time exceeded remaining quiz time",
                error_code="SESSION_EXPIRED_PAUSE_TIMEOUT",
                status_code=410,
            )

        # Handle specific error cases and graceful degradation
        if isinstance(result, dict):
            if result.get("error"):
                error_type = result.get("error")
                error_message = result.get("message", "Unknown error")
                
                if error_type == "session_not_found":
                    raise HTTPException(status_code=404, detail=error_message)
                elif error_type == "session_not_in_progress":
                    raise HTTPException(status_code=400, detail=error_message)
                elif error_type == "session_not_paused":
                    raise HTTPException(status_code=400, detail=error_message)
                else:
                    raise HTTPException(status_code=400, detail=error_message)
            elif result.get("success"):
                # Handle graceful degradation cases
                if result.get("session_not_found"):
                    return success_response(
                        data={"session_code": session_code, "resumed": False, "reason": "session_not_found"},
                        message="Session resume request processed (session not found)"
                    )
                elif result.get("session_not_in_progress"):
                    return success_response(
                        data={"session_code": session_code, "resumed": False, "reason": "session_not_in_progress"},
                        message="Session resume request processed (session not in progress)"
                    )
                elif result.get("session_not_paused"):
                    return success_response(
                        data={"session_code": session_code, "resumed": False, "reason": "session_not_paused"},
                        message="Session resume request processed (session not paused)"
                    )
                elif result.get("database_error"):
                    return success_response(
                        data={"session_code": session_code, "resumed": False, "reason": "database_error"},
                        message="Session resume request processed (database error)"
                    )

        if result:
            # Get updated session details with new remaining time
            session_details = get_session_and_assessment_details_by_code(session_code)

            if session_details:
                hashed_data = hash_ids_in_response(session_details)
                return success_response(
                    data=hashed_data, message="Session resumed successfully"
                )
            else:
                raise HTTPException(
                    status_code=404, detail="Session not found after resume"
                )
        else:
            raise HTTPException(
                status_code=400,
                detail="Could not resume session - session not found or not paused",
            )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error resuming session: {str(e)}", exc_info=True, extra={
            'session_code': session_code if 'session_code' in locals() else 'unknown',
            'endpoint': '/api/resume_session'
        })
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")


@app.get("/api/pause_status/{session_code}")
async def get_pause_status_endpoint(session_code: str):
    """
    Get detailed pause status for a session
    """
    try:
        session_code = decode_session_code_input(session_code)
        status = get_pause_status(session_code)

        if status.get("status") == "error":
            raise HTTPException(status_code=500, detail=status.get("error"))

        return success_response(
            data=status, message="Pause status retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting pause status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")


# New endpoint for single question
@app.get("/api/get_next_question/{session_code}")
def get_next_question(
    session_code: str,
    user_id: str = Query(...),
    difficulty: str = Query("easy"),
    retake: bool = Query(False),
):
    """
    Fetches the next quiz question for the user session.

    Args:
        session_code (str): The session code or hash.
        user_id (str): The external user ID of the quiz taker.
        difficulty (str, optional): The difficulty level. Defaults to "easy".
        retake (bool, optional): Whether to allow retakes. Defaults to False.

    Returns:
        dict: A dictionary containing the next question and session info.

    Raises:
        HTTPException
    """
    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        decoded_session_code = decode_session_code_input(session_code)
        # Get session details including user_id
        session_details = get_session_and_assessment_details_by_code(
            decoded_session_code
        )
        if not session_details:
            return error_response(
                message="Invalid or expired session code.",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Validate user
        session_user_id = None
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """
                        SELECT u.external_id
                        FROM users u
                        WHERE u.id = %s
                        """,
                        (session_details["user_id"],),
                    )
                    user_result = cur.fetchone()
                    if user_result:
                        session_user_id = user_result["external_id"]
                    else:
                        raise HTTPException(
                            status_code=404, detail="User not found for this session"
                        )
        except Exception as e:
            error(f"Error fetching user for session: {e}")
            raise HTTPException(
                status_code=500, detail=f"Error fetching user for session: {str(e)}"
            )

        # Validate user matches session
        if session_user_id and session_user_id != user_id:
            warning(
                f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}"
            )
            user_id = session_user_id

        # Get assessment details
        assessment_name_from_session = session_details["assessment_name"]
        question_topic_identifier = assessment_name_from_session.replace(
            " Mock Assessment", ""
        ).replace(" Final Assessment", "")

        is_final = session_details["is_final"]
        assessment_id = session_details["assessment_id"]

        # Get assessment details including question_selection_mode
        assessment_details = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment_details = cur.fetchone()

        is_fixed_assessment = (
            assessment_details
            and assessment_details["question_selection_mode"] == "fixed"
        )

        # Get attempted questions for this session
        attempted_questions = session_details.get("attempted_questions", [])

        # Get all available questions for this assessment
        if is_fixed_assessment:
            all_questions_for_topic = fetch_questions_for_fixed_assessment(
                assessment_id
            )
        else:
            # For dynamic assessments, get questions based on skills
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """SELECT skill_id FROM assessment_skills
                           WHERE assessment_id = %s""",
                        (assessment_id,),
                    )
                    results = cur.fetchall()
                    skill_ids = [row[0] for row in results] if results else []

            if skill_ids:
                all_questions_for_topic = fetch_questions_for_skills(
                    skill_ids, exclude_fixed_assessment_questions=True
                )
            else:
                if is_final:
                    all_questions_for_topic = fetch_final_questions(
                        question_topic_identifier
                    )
                else:
                    all_questions_for_topic = fetch_dynamic_questions_excluding_fixed(
                        question_topic_identifier
                    )

        # Filter questions to exclude attempted ones
        filtered_questions = []
        for question in all_questions_for_topic:
            # Skip attempted questions unless retake is allowed
            if question["que_id"] in attempted_questions and not retake:
                continue

            # Filter by difficulty if specified
            if difficulty != "all" and question["level"] != difficulty:
                continue

            filtered_questions.append(question)

        # If no questions available, return error
        if not filtered_questions:
            return error_response(
                message="No more questions available.",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Select next question randomly from available questions
        next_question = random.choice(filtered_questions)

        # Get session info
        remaining_time_seconds = session_details.get("remaining_time_seconds", None)
        attempted_questions_count = len(attempted_questions)

        # Get total questions for this assessment
        total_questions = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                if is_fixed_assessment:
                    cur.execute(
                        "SELECT COUNT(*) FROM assessment_questions WHERE assessment_id = %s",
                        (assessment_id,),
                    )
                    total_questions = cur.fetchone()[0]
                else:
                    cur.execute(
                        "SELECT total_questions FROM assessments WHERE id = %s",
                        (assessment_id,),
                    )
                    result = cur.fetchone()
                    if result and result[0]:
                        total_questions = result[0]

        # Filter out sensitive fields from question data
        filtered_question = {
            "que_id": next_question["que_id"],
            "topic": next_question["topic"],
            "level": next_question["level"],
            "question": next_question["question"],
            "options": next_question["options"],
        }

        # Calculate current score from user_answers
        current_score = 0
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """SELECT SUM(score) as total_score
                           FROM user_answers
                           WHERE session_id = %s""",
                        (session_details["session_id"],),
                    )
                    score_result = cur.fetchone()
                    if score_result and score_result[0]:
                        current_score = float(score_result[0])
        except Exception as e:
            error(f"Error calculating current score in get_next_question: {e}")

        # Get question_selection_mode
        question_selection_mode = "dynamic"  # Default
        if assessment_details and "question_selection_mode" in assessment_details:
            question_selection_mode = assessment_details["question_selection_mode"]

        # Format response with single question
        response_data = {
            "quiz_type": "final" if is_final else "mock",
            "quiz_name": assessment_name_from_session,
            "topic": question_topic_identifier,
            "question": filtered_question,
            "remaining_time_seconds": remaining_time_seconds,
            "attempted_questions_count": attempted_questions_count,
            "total_questions": total_questions,
            "current_score": current_score,
            "question_selection_mode": question_selection_mode,
            # "session_code": session_code
        }

        return success_response(
            data=response_data, message="Next question retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error(
            f"Error fetching next question for session code {session_code}: {str(e)}",
            exc_info=True,
        )
        raise_http_exception(
            status_code=500, detail=f"Failed to load next question: {str(e)}"
        )


@app.get(
    "/api/get_questions/{session_code}"
)  # Keep original endpoint for backward compatibility
def get_questions(
    session_code: str,  # Changed from quiz_code
    user_id: str = Query(...),  # External user_id of the quiz taker
    difficulty: str = Query("easy"),
    # Retake might be complex with new session model
    retake: bool = Query(False),
):
    """
    Fetches quiz questions based on the session code, user ID, difficulty.

    Args:
        session_code (str): The session code or hash.
        user_id (str): The external user ID of the quiz taker.
        difficulty (str, optional): The difficulty level. Defaults to "easy".
        retake (bool, optional): Whether to allow retakes. Defaults to False.
        (Note: retake logic might need review with session model)

    Returns:
        dict: A dictionary containing the quiz type, assessment name (as quiz_name),
              original topic (skill description), and a list of questions.

    Raises:
        HTTPException
    """

    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        decoded_session_code = decode_session_code_input(session_code)
        # Get session details including user_id
        session_details = get_session_and_assessment_details_by_code(
            decoded_session_code
        )
        if not session_details:
            return error_response(
                message="Invalid or expired session code.",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Always get the correct user_id from the session
        session_user_id = None
        try:
            # Get the external_id (username) for the user associated with this session
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """
                        SELECT u.external_id
                        FROM users u
                        WHERE u.id = %s
                        """,
                        (session_details["user_id"],),
                    )
                    user_result = cur.fetchone()
                    if user_result:
                        session_user_id = user_result["external_id"]
                    else:
                        error(
                            f"No user found for user_id: {session_details['user_id']}"
                        )
                        raise HTTPException(
                            status_code=404, detail="User not found for this session"
                        )
        except Exception as e:
            error(f"Error fetching user for session: {e}")
            raise HTTPException(
                status_code=500, detail=f"Error fetching user for session: {str(e)}"
            )

        # Always use the user_id from the session
        if session_user_id:
            if user_id and session_user_id != user_id:
                warning(
                    f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}"
                )
            # Always use the session's user_id
            user_id = session_user_id

        # Auto-start session if it's in pending status
        # This ensures that dynamic assessments work properly with pause/resume functionality
        if session_details["session_status"] == "pending":
            info(f"Auto-starting pending session {decoded_session_code}")
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        # Update session status to 'in_progress' and set started_at
                        cur.execute(
                            """UPDATE sessions
                               SET status = 'in_progress',
                                   started_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                               WHERE code = %s AND status = 'pending'""",
                            (decoded_session_code,),
                        )
                        conn.commit()
                        
                        if cur.rowcount > 0:
                            info(f"Successfully auto-started session {decoded_session_code}")
                            # Refresh session details to get updated status and timestamps
                            session_details = get_session_and_assessment_details_by_code(decoded_session_code)
                        else:
                            warning(f"Failed to auto-start session {decoded_session_code} - session may have changed status")
            except Exception as e:
                error(f"Error auto-starting session {decoded_session_code}: {e}")
                # Continue with the original session details - don't fail the request

        # Verify user if session is tied to a specific user (optional enhancement)
        # internal_user_id_from_session = session_details.get("user_id")
        # if internal_user_id_from_session:
        #     queried_user_internal_id = get_or_create_user(user_id) # Ensure user exists
        #     if internal_user_id_from_session != queried_user_internal_id:
        #         raise HTTPException(status_code=403, detail="User not authorized for this session.")

        # The 'topic' in questions table is assessment_base_name (e.g. UserAssessmentName_timestamp)
        # assessment_name in session_details is (e.g. UserAssessmentName_timestamp Mock Assessment)
        assessment_name_from_session = session_details["assessment_name"]
        question_topic_identifier = assessment_name_from_session.replace(
            " Mock Assessment", ""
        ).replace(" Final Assessment", "")

        is_final = session_details["is_final"]
        quiz_type = (
            "final" if is_final else "mock"
        )  # For logic requiring 'mock' or 'final' string

        # Get assessment details including question_selection_mode
        assessment_id = session_details["assessment_id"]
        assessment_details = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment_details = cur.fetchone()

        # Check if this is a fixed assessment
        is_fixed_assessment = (
            assessment_details
            and assessment_details["question_selection_mode"] == "fixed"
        )

        # For fixed assessments, get the pre-selected questions from assessment_questions table
        if is_fixed_assessment:
            all_questions_for_topic = fetch_questions_for_fixed_assessment(
                assessment_id
            )
            info(
                f"Fetched {len(all_questions_for_topic)} fixed questions for assessment {assessment_id}"
            )
            if len(all_questions_for_topic) > 0:
                info(
                    f"Sample fixed question: {all_questions_for_topic[0].get('question', 'N/A')[:50]}..."
                )
            else:
                warning(f"No fixed questions found for assessment {assessment_id}")
        else:
            # For dynamic assessments, get questions based on the skills associated with the assessment
            # First, get the skill IDs associated with this assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """SELECT skill_id FROM assessment_skills
                           WHERE assessment_id = %s""",
                        (assessment_id,),
                    )
                    results = cur.fetchall()
                    # Extract skill IDs from the results
                    skill_ids = []
                    if results:
                        for row in results:
                            skill_id = row[0]
                            skill_ids.append(skill_id)

            if skill_ids:
                # If we have skill IDs, fetch questions for these skills
                # For dynamic assessments, exclude questions that are assigned to fixed assessments

                all_questions_for_topic = fetch_questions_for_skills(
                    skill_ids, exclude_fixed_assessment_questions=True
                )

                if len(all_questions_for_topic) > 0:
                    info(
                        f"Sample dynamic question for skills {skill_ids}: "
                        f"{all_questions_for_topic[0].get('question', 'N/A')[:50]}..."
                    )
                else:
                    warning(f"No dynamic questions found for skills {skill_ids}")
            else:
                # Fallback to the original logic if no skills are associated
                # For dynamic assessments, also exclude fixed assessment questions
                if is_final:
                    all_questions_for_topic = fetch_final_questions(
                        question_topic_identifier
                    )
                else:
                    # For dynamic assessments, fetch questions but exclude those assigned to fixed assessments
                    all_questions_for_topic = fetch_dynamic_questions_excluding_fixed(
                        question_topic_identifier
                    )
                    info(
                        f"Fetched {len(all_questions_for_topic)} questions for topic: "
                        f"{question_topic_identifier} (excluding fixed assessment questions)"
                    )

        # For in-progress sessions, get attempted questions from the session details
        # This ensures we don't show questions the user has already answered in this session
        if session_details.get("attempted_questions") and not retake:
            attempted_questions = session_details.get("attempted_questions", [])
            info(
                f"Found {len(attempted_questions)} attempted questions for in-progress session {session_code}"
            )
        else:
            # Fallback to legacy method for backward compatibility
            attempted_questions = (
                []
                if retake
                else fetch_attempted_question_ids(question_topic_identifier, user_id)
            )

        # For fixed assessments, we don't need to filter by final question IDs
        # For dynamic assessments, we need to handle final questions appropriately
        if is_fixed_assessment:
            # For fixed assessments, we only filter out attempted questions
            # and filter by difficulty if specified
            filtered_questions = []
            for question in all_questions_for_topic:
                # Skip attempted questions unless retake is allowed
                if question["que_id"] in attempted_questions and not retake:
                    continue

                # For fixed assessments, if difficulty is specified, filter by it
                # Otherwise, include all questions regardless of difficulty
                if difficulty != "all" and question["level"] != difficulty:
                    continue

                filtered_questions.append(question)
        else:
            # If it's a final quiz, all_questions_for_topic are already the selected final questions.
            # If it's mock, all_questions_for_topic are all questions for that base name.
            final_question_ids_for_topic = (
                get_final_question_ids(question_topic_identifier)
                if not is_final
                else []
            )

            filtered_questions = filter_questions(
                all_questions_for_topic,
                attempted_questions,
                # Only relevant for mock to exclude questions already in a final set
                final_question_ids_for_topic,
                is_final,
                difficulty,
                allow_retake=not is_final and retake,  # Retake only for mock
            )

        # Determine number of questions to return
        num_questions_to_return = 0

        if is_fixed_assessment:
            # For fixed assessments, return all filtered questions
            # This ensures we return the exact set of questions configured for this assessment
            num_questions_to_return = len(filtered_questions)
        elif is_final:
            # For final dynamic assessments, it should be based on assessment.total_questions
            # For now, let's assume it returns all available non-attempted final questions
            num_questions_to_return = len(filtered_questions)
            # A better approach for final would be to fetch assessment.total_questions
            # and ensure the user gets that many, possibly from a pre-shuffled list for the session.
        else:  # Mock quiz with dynamic questions
            # Use assessment's total_questions instead of environment variable
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        cur.execute(
                            "SELECT total_questions FROM assessments WHERE id = %s",
                            (assessment_id,),
                        )
                        result = cur.fetchone()
                        if result:
                            num_questions_to_return = result[0]
                        else:
                            # Fallback to environment variable if assessment not found
                            mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
                            num_questions_to_return = int(mock_q_count_env)
            except Exception as e:
                error(f"Error fetching assessment total_questions: {e}")
                # Fallback to environment variable
                mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
                try:
                    num_questions_to_return = int(mock_q_count_env)
                except ValueError:
                    num_questions_to_return = 10

        if not filtered_questions:
            error(
                f"No filtered questions available. Original questions: {len(all_questions_for_topic)}"
            )
            return error_response(
                message=get_error_message(
                    is_final, question_topic_identifier, difficulty
                ),
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Sample questions if more are available than needed (especially for mock)
        if is_fixed_assessment:
            # For fixed assessments, don't sample - return all filtered questions in order
            selected_questions = filtered_questions
        elif len(filtered_questions) > num_questions_to_return:
            # For dynamic assessments with skills, try to get a balanced distribution from each skill
            if not is_fixed_assessment and skill_ids and len(skill_ids) > 1:
                # Group questions by skill
                questions_by_skill = {}
                for q in filtered_questions:
                    skill_id = q.get("skill_id")
                    if skill_id:
                        if skill_id not in questions_by_skill:
                            questions_by_skill[skill_id] = []
                        questions_by_skill[skill_id].append(q)

                # Calculate how many questions to take from each skill
                questions_per_skill = num_questions_to_return // len(questions_by_skill)
                remainder = num_questions_to_return % len(questions_by_skill)

                # Select questions from each skill
                selected_questions = []
                for skill_id, questions in questions_by_skill.items():
                    # Take extra question from skills until remainder is used up
                    skill_question_count = questions_per_skill + (
                        1 if remainder > 0 else 0
                    )
                    remainder -= 1 if remainder > 0 else 0

                    # If we have more questions than needed for this skill, sample randomly
                    if len(questions) > skill_question_count:
                        selected_questions.extend(
                            random.sample(questions, skill_question_count)
                        )
                    else:
                        selected_questions.extend(questions)

                # If we still need more questions (due to some skills having fewer questions than allocated)
                if len(selected_questions) < num_questions_to_return:
                    # Get all questions that weren't selected
                    remaining_questions = [
                        q for q in filtered_questions if q not in selected_questions
                    ]
                    # Sample from remaining questions to fill the gap
                    additional_needed = num_questions_to_return - len(
                        selected_questions
                    )
                    if remaining_questions and additional_needed > 0:
                        if len(remaining_questions) > additional_needed:
                            selected_questions.extend(
                                random.sample(remaining_questions, additional_needed)
                            )
                        else:
                            selected_questions.extend(remaining_questions)
            else:
                # For single skill or non-skill-based assessments, just sample randomly
                selected_questions = random.sample(
                    filtered_questions, num_questions_to_return
                )
        else:
            selected_questions = filtered_questions

        if not selected_questions:  # Double check after sampling
            raise HTTPException(
                status_code=404,
                detail=get_error_message(
                    is_final, question_topic_identifier, difficulty
                ),
            )

        # Fetch original skill description to pass as "topic" in response for user context
        # This requires joining assessment_skills and skills table.
        # For simplicity now, we'll pass assessment_name_from_session as topic.
        # A better "topic" would be the skill.description.
        # Let's assume `assessment_details.description` (from get_or_create_assessment) is good enough.
        # The `session_details` doesn't directly have the assessment description.
        # We can fetch it if needed:
        assessment_description_for_user = get_assessment_description(
            session_details["assessment_id"]
        )

        # Get total questions for this assessment
        total_questions = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                if is_fixed_assessment:
                    # For fixed assessments, count questions in assessment_questions
                    cur.execute(
                        "SELECT COUNT(*) FROM assessment_questions WHERE assessment_id = %s",
                        (session_details["assessment_id"],),
                    )
                    total_questions = cur.fetchone()[0]
                else:
                    # For dynamic assessments, use the assessment's total_questions field
                    cur.execute(
                        "SELECT total_questions FROM assessments WHERE id = %s",
                        (session_details["assessment_id"],),
                    )
                    result = cur.fetchone()
                    if result and result[0]:
                        total_questions = result[0]

        # Get attempted questions count
        attempted_questions_count = len(session_details.get("attempted_questions", []))

        # Get remaining time
        remaining_time_seconds = session_details.get("remaining_time_seconds", None)

        formatted_data = format_response(
            quiz_type,
            # Full assessment name like "MyQuiz_ts Mock Assessment"
            assessment_name_from_session,
            assessment_description_for_user
            or question_topic_identifier,  # User-facing topic
            selected_questions,
            remaining_time_seconds=remaining_time_seconds,
            attempted_questions_count=attempted_questions_count,
            total_questions=total_questions,
        )
        return success_response(
            data=formatted_data, message="Questions retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error(
            f"Error fetching questions for session code {session_code}: {str(e)}",
            exc_info=True,
        )
        raise_http_exception(
            status_code=500, detail=f"Failed to load questions: {str(e)}"
        )


def filter_questions(
    all_questions,
    attempted,
    final_ids_to_exclude_from_mock,
    is_final,
    difficulty,
    allow_retake=False,
):
    """Filter questions based on quiz type and difficulty."""
    if is_final:
        # For final quizzes, all_questions are already the selected final questions.
        # Filter out attempted ones.
        if difficulty != "all":
            # If a specific difficulty is requested, filter by it
            filtered_by_difficulty = get_questions_by_difficulty(
                all_questions, difficulty
            )
            return [q for q in filtered_by_difficulty if q["que_id"] not in attempted]
        else:
            # If all difficulties are requested, just filter out attempted ones
            return [q for q in all_questions if q["que_id"] not in attempted]

    # For mock quizzes
    if difficulty != "all":
        # If a specific difficulty is requested, filter by it
        questions_of_difficulty = get_questions_by_difficulty(all_questions, difficulty)
    else:
        # If all difficulties are requested, use all questions
        questions_of_difficulty = all_questions

    # Filter out attempted questions (if not retake) and questions already in a final set for this topic
    return [
        q
        for q in questions_of_difficulty
        if (allow_retake or q["que_id"] not in attempted)
        and q["que_id"] not in final_ids_to_exclude_from_mock
    ]


def get_error_message(is_final, quiz_name_base, difficulty):
    """Generate an appropriate error message."""
    if is_final:
        return f"No more final questions available for assessment: {quiz_name_base}, or you have attempted all."
    else:
        return (
            f"No new '{difficulty}' questions available for assessment: {quiz_name_base}. "
            "This could be because: 1) All questions have been attempted, "
            "2) All questions are assigned to fixed assessments, "
            "or 3) No questions exist for this difficulty level. Try another difficulty or create more questions."
        )


def format_response(
    quiz_type,
    assessment_full_name,
    user_facing_topic,
    selected_questions,
    remaining_time_seconds=None,
    attempted_questions_count=None,
    total_questions=None,
):
    """Format response JSON."""
    response = {
        "quiz_type": quiz_type,
        # e.g. "MyAssessment_timestamp Mock Assessment"
        "quiz_name": assessment_full_name,
        "topic": user_facing_topic,  # Skill description or similar for user context
        "question": [
            {
                "que_id": q["que_id"],
                "question": q["question"],
                "options": q["options"],
                "level": q["level"],
            }
            for q in selected_questions
        ],
    }

    # Add session progress information if available
    if remaining_time_seconds is not None:
        response["remaining_time_seconds"] = remaining_time_seconds

    if attempted_questions_count is not None:
        response["attempted_questions_count"] = attempted_questions_count

    if total_questions is not None:
        response["total_questions"] = total_questions
        response["questions_left"] = max(
            0, total_questions - (attempted_questions_count or 0)
        )

    return response


@app.post("/api/check_and_save_answer")
def check_and_save_answer_endpoint(
    # Contains session_code now
    request: AnswerRequest,
    _: None = Depends(rate_limiter),
):
    """
    Checks and saves the user's answer for a specific quiz question using session_code.
    """
    # The internal check_and_save_answer function now handles fetching session details
    result = check_and_save_answer(
        user_id=request.user_id,
        question_id=request.question_id,
        answer=request.answer,
        session_code=request.session_code,
        time_taken=request.time_taken,
    )
    return result  # check_and_save_answer already returns standardized response


@app.post("/api/submit_session")
def submit_session(request: SessionSubmissionRequest, _: None = Depends(rate_limiter)):
    """
    Submit a quiz session, marking it as completed and calculating final score.
    """
    try:
        session_code_input = request.session_code
        user_id = request.user_id

        # Add detailed logging for debugging
        debug(f"Submit session request: session_code='{session_code_input}', user_id='{user_id}'")

        # Validate input parameters
        if not session_code_input:
            raise HTTPException(
                status_code=400, detail="Session code is required."
            )
        
        if not user_id:
            raise HTTPException(
                status_code=400, detail="User ID is required."
            )

        # Decode session code (handles both raw 6-digit codes and hashes)
        session_code = decode_session_code_input(session_code_input)
        debug(f"Decoded session code: '{session_code}'")

        # Get session details
        session_details = get_session_and_assessment_details_by_code(session_code)
        if not session_details:
            debug(f"Session not found for code: '{session_code}'")
            raise HTTPException(
                status_code=404, detail="Invalid or expired session code."
            )

        debug(f"Session details: status='{session_details['session_status']}', session_id={session_details.get('session_id')}")

        # Verify session is in progress or handle edge cases
        current_status = session_details["session_status"]
        
        if current_status == "completed":
            # Session is already completed - return the existing completion data
            debug(f"Session already completed, returning existing data")
            
            # Get the existing completion data
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """SELECT score, completed_at FROM sessions WHERE id = %s""",
                        (session_details["session_id"],),
                    )
                    result = cur.fetchone()
                    if result:
                        # Return the existing completion data
                        data = {
                            "session_id": session_details["session_id"],
                            "obtained_score": result["score"] or 0,
                            "status": "completed",
                            "message": "Session was already completed"
                        }
                        hashed_data = hash_ids_in_response(data)
                        return success_response(
                            data=hashed_data, message="Session was already completed"
                        )
            
            # If we can't get the existing data, still return success
            return success_response(
                data={"status": "completed", "message": "Session was already completed"},
                message="Session was already completed"
            )
        
        elif current_status == "expired":
            debug(f"Session expired, but attempting to complete anyway")
            # For expired sessions, we still try to complete them to save any progress
            # Update the session status to completed
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """UPDATE sessions 
                           SET status = 'completed', completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                           WHERE id = %s 
                           RETURNING id""",
                        (session_details["session_id"],),
                    )
                    result = cur.fetchone()
                    if result:
                        conn.commit()
                        debug(f"Successfully completed expired session {session_details['session_id']}")
                        return success_response(
                            data={"status": "completed", "message": "Expired session completed"},
                            message="Session completed successfully"
                        )
        
        elif current_status != "in_progress":
            debug(f"Session not in progress. Current status: {current_status}")
            raise HTTPException(
                status_code=400,
                detail=f"Session is not in progress. Current status: {current_status}",
            )

        # Verify user matches session
        internal_user_id = get_or_create_user(user_id)
        debug(f"User validation: internal_user_id={internal_user_id}, session_user_id={session_details.get('user_id')}")
        
        if session_details["user_id"] != internal_user_id:
            debug(f"User mismatch: session belongs to user_id={session_details['user_id']}, but request from user_id={internal_user_id}")
            raise HTTPException(status_code=403, detail="User does not match session.")

        # Import the new scoring functions
        from app.models.db_manager import (
            calculate_total_score_for_assessment,
            get_performance_level_with_correct_total,
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Calculate obtained score from user_answers
                cur.execute(
                    """SELECT COALESCE(SUM(score), 0) as obtained_score
                       FROM user_answers
                       WHERE session_id = %s""",
                    (session_details["session_id"],),
                )
                score_result = cur.fetchone()
                obtained_score = score_result["obtained_score"] if score_result else 0

                # Calculate correct total possible score based on assessment mode
                assessment_id = session_details["assessment_id"]
                session_id = session_details["session_id"]
                total_possible_score = calculate_total_score_for_assessment(
                    assessment_id, session_id
                )

                # Calculate performance level with correct total score
                performance_level = get_performance_level_with_correct_total(
                    obtained_score, assessment_id, session_id
                )

                # Update session to completed
                cur.execute(
                    """UPDATE sessions
                       SET status = 'completed',
                           score = %s,
                           completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                       WHERE id = %s AND status = 'in_progress'
                       RETURNING id""",
                    (obtained_score, session_details["session_id"]),
                )

                updated_session = cur.fetchone()
                if not updated_session:
                    raise HTTPException(
                        status_code=400, detail="Session could not be completed."
                    )

                conn.commit()

                # Calculate percentage
                percentage = (
                    (obtained_score / total_possible_score * 100)
                    if total_possible_score > 0
                    else 0
                )

                data = {
                    "session_id": session_details["session_id"],
                    "obtained_score": obtained_score,
                    "total_possible_score": total_possible_score,
                    "percentage": round(percentage, 2),
                    "performance_level": performance_level,
                    "status": "completed",
                }
                hashed_data = hash_ids_in_response(data)
                return success_response(
                    data=hashed_data, message="Session submitted successfully"
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error submitting session {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(
            status_code=500, detail=f"Error submitting session: {str(e)}"
        )


@app.get("/api/get_progress")
def get_progress(
    user_id: str = Query(...),  # External user_id
    assessment_name: str = Query(
        ...
    ),  # Full assessment name, e.g. "MyQuiz_timestamp Mock Assessment"
    # quiz_type is implicitly defined by assessment_name containing "Mock" or "Final"
):
    """
    Retrieves the progress of a user on a specific assessment.
    The `assessment_name` should be the full name like "Topic_timestamp Assessment".
    """
    try:
        quiz_type_str = "assessment"  # Always use 'assessment' type

        # Base assessment name (topic for user_assessment table)
        assessment_base_name = assessment_name.replace(" Assessment", "")

        # Try to get progress from new schema first (user_answers via sessions)
        try:
            internal_user_id = get_or_create_user(user_id)

            # Find assessment_id from assessment_name
            db_assessment_id = None
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        "SELECT id FROM assessments WHERE name = %s", (assessment_name,)
                    )
                    assessment_row = cur.fetchone()
                    if assessment_row:
                        db_assessment_id = assessment_row["id"]

            if not db_assessment_id:
                warning(
                    f"No assessment found with name {assessment_name} for progress."
                )
                # Fall through to legacy, or raise error. Let's fall through for now.

            if db_assessment_id:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                        # Get the latest session for this user and assessment
                        cur.execute(
                            """SELECT s.id FROM sessions s
                               WHERE s.user_id = %s AND s.assessment_id = %s
                               ORDER BY s.created_at DESC LIMIT 1""",
                            (internal_user_id, db_assessment_id),
                        )
                        session_result = cur.fetchone()

                        if session_result:
                            session_id = session_result["id"]
                            cur.execute(
                                """SELECT ua.*, q.question, q.options, q.answer as correct_answer_key, q.level
                                   FROM user_answers ua
                                   JOIN questions q ON ua.question_id = q.que_id
                                   WHERE ua.session_id = %s
                                   ORDER BY ua.created_at""",
                                (session_id,),
                            )
                            answers = cur.fetchall()

                            if answers:
                                progress_data = []
                                total_score_val = 0

                                for answer_row in answers:
                                    result_text = (
                                        "Correct"
                                        if answer_row["is_correct"]
                                        else "Incorrect"
                                    )
                                    if (
                                        answer_row["user_answer"]
                                        and answer_row["user_answer"].lower()
                                        == "timeout"
                                    ):
                                        result_text = "Timeout"

                                    progress_data.append(
                                        {
                                            "question": answer_row["question"],
                                            "options": answer_row["options"],
                                            "correct_answer": answer_row[
                                                "correct_answer_key"
                                            ],  # This is the key 'a', 'b' etc.
                                            "user_answer": answer_row["user_answer"]
                                            or "None",
                                            "result": result_text,
                                            "score": answer_row["score"],
                                        }
                                    )
                                    total_score_val += answer_row["score"]

                                # Calculate correct performance level using the new scoring functions
                                from app.models.db_manager import (
                                    get_performance_level_with_correct_total,
                                )

                                user_level_val = (
                                    get_performance_level_with_correct_total(
                                        total_score_val, db_assessment_id, session_id
                                    )
                                )

                                # Also calculate total possible score for display
                                from app.models.db_manager import (
                                    calculate_total_score_for_assessment,
                                )

                                total_possible_score = (
                                    calculate_total_score_for_assessment(
                                        db_assessment_id, session_id
                                    )
                                )
                                percentage = (
                                    (total_score_val / total_possible_score * 100)
                                    if total_possible_score > 0
                                    else 0
                                )

                                return success_response(
                                    data={
                                        "obtained_score": total_score_val,
                                        "total_possible_score": total_possible_score,
                                        "percentage": round(percentage, 2),
                                        "user_level": user_level_val,
                                        "progress_report": progress_data,
                                    },
                                    message="Progress retrieved successfully",
                                )
        except Exception as e:
            warning(
                f"Error getting progress from new schema for {user_id} on {assessment_name}, falling back: {e}",
                exc_info=True,
            )

        # Fall back to old schema (user_assessment table)
        # fetch_user_progress expects assessment_base_name and quiz_type_str
        info(
            "Falling back to legacy progress for user %s, assessment base %s, type %s",
            user_id,
            assessment_base_name,
            quiz_type_str,
        )

        progress_data_legacy = fetch_user_progress(
            assessment_base_name, user_id, quiz_type_str
        )
        if not progress_data_legacy:  # If legacy also returns empty, then no progress.
            return success_response(
                data={
                    "total_score": 0,
                    "user_level": "N/A",
                    "progress_report": [],
                },
                message="No progress found",
            )

        logging.debug(f"Legacy Progress Data: {progress_data_legacy}")
        total_score_legacy = sum(item["score"] for item in progress_data_legacy)
        user_level_legacy = get_performance_level(total_score_legacy)  # Simpler version
        return success_response(
            data={
                "total_score": total_score_legacy,
                "user_level": user_level_legacy,
                "progress_report": progress_data_legacy,
            },
            message="Progress retrieved successfully",
        )
    except Exception as e:
        logging.error(
            f"Error in get_progress for {user_id} on {assessment_name}: {str(e)}",
            exc_info=True,
        )
        raise_http_exception(
            status_code=500, detail=f"Error retrieving progress: {str(e)}"
        )


@app.post("/api/check_user")
def check_user(request: UserCheckRequest):
    """
    Check if a user is in the list of allowed users.
    """
    if request.user_id in ALLOWED_USERS:
        return success_response(data={"status": "true"}, message="User is allowed")
    return success_response(
        data={"status": "false"}, message="User is not in allowed list"
    )


@app.get("/api/get_question_counts")
def get_question_counts():
    """
    Fetch question counts for different difficulty levels for final assessments.
    """
    n = int(os.getenv("FINAL_QUESTION_COUNT", "20"))
    easy, intermediate, advanced = divide_number(n)
    return success_response(
        data={"easy": easy, "intermediate": intermediate, "advanced": advanced},
        message="Question counts retrieved successfully",
    )


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check database connection
        import psycopg2

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()

        if result and result[0] == 1:
            return success_response(
                data={"status": "healthy", "database": "connected"},
                message="Health check passed",
            )
        else:
            error("Health check: DB query did not return 1")
            return JSONResponse(
                status_code=503,
                content=error_response(
                    message="Health check failed",
                    code=503,
                    error_type="ServiceUnavailable",
                    details={"database": "error", "detail": "DB query failed"},
                ),
            )
    except Exception as e:
        error("Health check failed", exception=e)
        return JSONResponse(
            status_code=503,
            content=error_response(
                message="Health check failed",
                code=503,
                error_type="ServiceUnavailable",
                details={"error": str(e)},
            ),
        )


# Skills management endpoints
class CreateSkillRequest(BaseModel):
    name: str
    description: str = ""


@app.post("/api/admin/skills")
async def create_skill(request: CreateSkillRequest, _: None = Depends(rate_limiter)):
    """Create a new skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """INSERT INTO skills (name, description)
                       VALUES (%s, %s) RETURNING id, name, description""",
                    (request.name, request.description),
                )
                skill = dict(cur.fetchone())
                conn.commit()
                hashed_skill = hash_ids_in_response(skill)
                return success_response(
                    data={"skill": hashed_skill}, message="Skill created successfully"
                )
    except psycopg2.errors.UniqueViolation:
        raise HTTPException(
            status_code=400, detail="A skill with this name already exists"
        )
    except Exception as e:
        error(f"Error creating skill: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating skill: {str(e)}")


@app.get("/api/admin/skills")
async def get_skills(
    limit: int = Query(7, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get skills with pagination

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total count first
                cur.execute("SELECT COUNT(*) FROM skills")
                total = cur.fetchone()[0]

                # Get paginated results with question count
                cur.execute(
                    """
                    SELECT s.id, s.name, s.description, s.created_at, s.updated_at,
                           COUNT(q.que_id) as question_count
                    FROM skills s
                    LEFT JOIN questions q ON s.id = q.skill_id
                    GROUP BY s.id, s.name, s.description, s.created_at, s.updated_at
                    ORDER BY s.name
                    LIMIT %s OFFSET %s
                """,
                    (limit, offset),
                )
                skills = [dict(row) for row in cur.fetchall()]

                # Transform response to include hashed IDs
                hashed_skills = hash_ids_in_response(skills)

                # Return paginated response
                return paginated_response(
                    data=hashed_skills,
                    total=total,
                    limit=limit,
                    offset=offset,
                    message="Skills retrieved successfully",
                )
    except Exception as e:
        error(f"Error getting skills: {str(e)}")
        return error_response(
            message="Failed to retrieve skills",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/skills/{skill_id}")
async def get_skill(skill_id: str):
    """Get a single skill with its details"""
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = skill_id
        if not skill_id.isdigit():
            decoded_id = decode_skill_id(skill_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                # Check if it's a hash for a different entity type
                from .utils.hashid_utils import detect_hash_type

                detected_type = detect_hash_type(skill_id)
                if detected_type:
                    return error_response(
                        message=f"Invalid hash type: '{skill_id}' is a {detected_type} hash, not a skill hash",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )
                else:
                    return error_response(
                        message=f"Invalid skill ID format: {skill_id}",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )
        else:
            actual_id = int(skill_id)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get skill details
                cur.execute(
                    "SELECT id, name, description, created_at, updated_at FROM skills WHERE id = %s",
                    (actual_id,),
                )
                skill = cur.fetchone()

                if not skill:
                    return error_response(
                        message=f"Skill with ID {actual_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                skill_dict = dict(skill)

                # Get question count for this skill
                cur.execute(
                    "SELECT COUNT(*) as question_count FROM questions WHERE skill_id = %s",
                    (actual_id,),
                )
                count_result = cur.fetchone()
                skill_dict["question_count"] = (
                    count_result["question_count"] if count_result else 0
                )

                # Transform response to include hashed IDs
                hashed_skill = hash_ids_in_response(skill_dict)

                return success_response(
                    data=hashed_skill,
                    message=f"Skill '{skill_dict['name']}' retrieved successfully",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching skill {skill_id}: {str(e)}")
        return error_response(
            message=f"Error fetching skill: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/skill-question-counts")
async def get_skill_question_counts():
    """Get question counts for each skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT skill_id, COUNT(*) as count
                    FROM questions
                    WHERE skill_id IS NOT NULL
                    GROUP BY skill_id
                """
                )
                counts = {row["skill_id"]: row["count"] for row in cur.fetchall()}

                # Format as a list of objects for better API consistency
                count_list = [
                    {"skill_id": skill_id, "count": count}
                    for skill_id, count in counts.items()
                ]

                return success_response(
                    data=count_list,
                    message="Skill question counts retrieved successfully",
                )
    except Exception as e:
        error(f"Error getting skill question counts: {str(e)}")
        return error_response(
            message=f"Error getting skill question counts: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.post("/api/admin/generate-skill-questions")
async def generate_skill_questions(
    request: GenerateSkillQuestionsRequest, _: None = Depends(rate_limiter)
):
    """Generate questions for a specific skill using Dapr Pub/Sub"""
    try:
        skill_id = request.skillId
        skill_name = request.skillName
        skill_description = request.skillDescription

        # Validate inputs
        if not skill_id or not skill_name or not skill_description:
            return error_response(
                message="Skill ID, name, and description are required",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        # Try to convert skill_id to int if it's a string
        if isinstance(skill_id, str):
            try:
                # Check if it's a hash ID
                from .utils.hashid_utils import decode_skill_id

                decoded_id = decode_skill_id(skill_id)
                if decoded_id is not None:
                    skill_id = decoded_id
                    info(f"Converted hash ID {skill_id} to numeric ID {decoded_id}")
                else:
                    # Try to convert to int directly
                    try:
                        skill_id = int(skill_id)
                        info(f"Converted string ID {skill_id} to numeric ID")
                    except ValueError:
                        # If we can't convert to int, try to find the skill by name
                        with psycopg2.connect(**DATABASE_CONFIG) as conn:
                            with conn.cursor() as cur:
                                cur.execute(
                                    "SELECT id FROM skills WHERE name = %s",
                                    (skill_name,),
                                )
                                result = cur.fetchone()
                                if result:
                                    skill_id = result[0]
                                    info(
                                        f"Found skill ID {skill_id} by name {skill_name}"
                                    )
                                else:
                                    return error_response(
                                        message=(
                                            f"Invalid skill ID format: {skill_id} and could not find skill by name: "
                                            f"{skill_name}"
                                        ),
                                        code=status.HTTP_400_BAD_REQUEST,
                                        error_type="BadRequest",
                                    )
            except Exception as e:
                error(f"Error processing skill ID: {e}")
                return error_response(
                    message=f"Invalid skill ID format: {skill_id}. Error: {str(e)}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )

        if len(skill_description.strip()) < 20:
            return error_response(
                message="Skill description must be at least 20 characters long for effective question generation",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        # Check environment variables for question generation
        model_id = os.getenv("MODEL_ID")
        if not model_id:
            return error_response(
                message="Question generation service is not properly configured. MODEL_ID is missing.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_type="InternalServerError",
            )

        # Check if API credentials are available
        base_url = os.getenv("BASE_URL")
        api_key = os.getenv("API_KEY")
        if not base_url or not api_key:
            return error_response(
                message="Question generation service is not properly configured. API credentials are missing.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_type="InternalServerError",
            )

        # Check if the skill exists and get current question count
        existing_count = 0
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute("SELECT id FROM skills WHERE id = %s", (skill_id,))
                if not cur.fetchone():
                    return error_response(
                        message=f"Skill with ID {skill_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                # Get current question count
                cur.execute(
                    "SELECT COUNT(*) as count FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                result = cur.fetchone()
                existing_count = result["count"] if result else 0

        info(
            f"Publishing question generation task for skill: {skill_name} (ID: {skill_id})"
        )

        # Create task payload
        task_data = {
            "skill_id": skill_id,
            "skill_name": skill_name,
            "skill_description": skill_description,
            # Simple task ID for tracking
            "task_id": f"skill_{skill_id}_{int(time.time())}",
        }

        # Try to publish to Dapr Pub/Sub, fallback to direct worker call
        dapr_success = False

        try:
            dapr_endpoint = os.getenv("DAPR_ENDPOINT", "http://localhost:3500")
            dapr_url = f"{dapr_endpoint}/v1.0/publish/pubsub/generate-questions"

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    dapr_url,
                    json=task_data,
                    headers={"Content-Type": "application/json"},
                    timeout=5.0,  # Shorter timeout for Dapr
                )

                if (
                    response.status_code == 204
                ):  # Dapr returns 204 for successful publish
                    dapr_success = True
                    info("Successfully published task to Dapr")
                else:
                    warning(
                        f"Dapr publish failed: {response.status_code} - {response.text}"
                    )

        except (httpx.TimeoutException, httpx.RequestError, Exception) as e:
            warning(f"Dapr not available ({e}), falling back to direct worker call")

        # Fallback: Direct call to worker if Dapr failed
        if not dapr_success:
            try:
                worker_endpoint = os.getenv("WORKER_ENDPOINT", "http://localhost:8001")
                worker_url = f"{worker_endpoint}/generate-questions"

                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        worker_url,
                        json=task_data,
                        headers={"Content-Type": "application/json"},
                        timeout=10.0,
                    )

                    if response.status_code != 200:
                        error(
                            f"Failed to call worker directly: {response.status_code} - {response.text}"
                        )
                        raise HTTPException(
                            status_code=500,
                            detail="Failed to start question generation task. Please try again.",
                        )

                    info("Successfully sent task to worker directly")

            except httpx.TimeoutException:
                # Don't treat timeout as an error - the task is still running in the background
                info(
                    "Worker call timed out, but task is likely still processing in background"
                )
                # Continue to return success since the task was started
            except httpx.RequestError as e:
                error(f"Network error while calling worker: {e}")
                raise HTTPException(
                    status_code=500,
                    detail="Network error while starting task. Please check if the worker service is running.",
                )
            except Exception as e:
                error(f"Unexpected error while calling worker: {e}")
                raise HTTPException(
                    status_code=500,
                    detail="An unexpected error occurred. Please try again.",
                )

        info(f"Successfully published question generation task for skill: {skill_name}")

        return {
            "success": True,
            "message": f"Question generation task started for skill: {skill_name}. "
            f"Currently has {existing_count} questions. Check back in a few minutes for new questions.",
            "task_id": task_data["task_id"],
            "current_question_count": existing_count,
            "status": "task_started",
        }

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating questions for skill: {str(e)}", exc_info=True)

        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error during question generation: {str(e)}. "
            f"Please try again or contact support if the problem persists.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/question-generation-status/{skill_id}")
async def get_question_generation_status(skill_id: str):
    """Get the current question count for a skill (to check generation progress)"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get current question count
                cur.execute(
                    "SELECT COUNT(*) as count FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                result = cur.fetchone()
                question_count = result["count"] if result else 0

                # Get skill name for context
                cur.execute(
                    "SELECT name FROM skills WHERE id = %s",
                    (skill_id,),
                )
                skill_result = cur.fetchone()
                skill_name = skill_result["name"] if skill_result else "Unknown"

                return {
                    "skill_id": skill_id,
                    "skill_name": skill_name,
                    "question_count": question_count,
                    "status": "completed" if question_count > 0 else "pending",
                }

    except Exception as e:
        error(f"Error getting question generation status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting question generation status: {str(e)}",
        )


@app.post("/api/admin/suggest-skill-description")
async def suggest_skill_description(
    request: SuggestSkillDescriptionRequest, _: None = Depends(rate_limiter)
):
    """Suggest a skill description using LLM"""
    try:
        # Load the prompt template from prompt.yaml file
        description_prompt_template = await load_yaml_prompt("description_prompt")
        if not description_prompt_template:
            error("Failed to load description prompt template.")
            return success_response(
                data={
                    "description": "Failed to load prompt template. Please try again or enter manually.",
                    "level": "intermediate",
                    "level_color": "yellow",
                },
                message="Failed to load prompt template",
            )

        # Replace variables in the template
        template = Template(description_prompt_template)
        variables = {
            "skill_name": request.skill_name,
            "description": request.existing_description
            or f"A skill related to {request.skill_name}",
        }

        prompt = template.substitute(variables)

        # Get the model ID from environment variables
        model_id = os.getenv("MODEL_ID")

        # Query the model directly using the ollama_client
        response = await query_model(prompt, model_id)

        # Extract the description and level from the response
        debug(f"Full response: {response}")

        if response and "choices" in response and len(response["choices"]) > 0:
            # Extract the content from the first choice
            choice = response["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                response_text = choice["message"]["content"].strip()
            elif "text" in choice:
                response_text = choice["text"].strip()
            else:
                error(f"Unexpected response structure: {response}")
                return success_response(
                    data={
                        "description": "Failed to extract content from API response. "
                        "Please try again or enter manually.",
                        "level": "intermediate",
                        "level_color": "yellow",
                    },
                    message="Failed to extract content from API response",
                )

            # Parse the response to extract improved description and level
            improved_description_match = re.search(
                r"Improved Description:\s*(.*?)(?:\n|$)", response_text
            )
            level_match = re.search(r"Level:\s*(.*?)(?:\n|$)", response_text)

            if improved_description_match:
                description = improved_description_match.group(1).strip()
            else:
                # If the specific format isn't found, use the whole response as the description
                description = response_text

            level = level_match.group(1).strip() if level_match else "intermediate"

            # Add color formatting to the level
            level_color = {
                "easy": "green",
                "intermediate": "yellow",
                "advanced": "orange",
            }.get(
                level.lower(), "yellow"
            )  # Default to yellow if level is not recognized

            return success_response(
                data={
                    "description": description,
                    "level": level,
                    "level_color": level_color,
                },
                message="Successfully generated skill description",
            )
        else:
            error(f"Invalid response structure: {response}")
            return success_response(
                data={
                    "description": "Failed to generate description. Please try again or enter manually.",
                    "level": "intermediate",
                    "level_color": "yellow",
                },
                message="Failed to generate description",
            )
    except Exception as e:
        error(f"Error suggesting skill description: {str(e)}")
        return error_response(
            message=f"Error generating skill description: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.post("/api/admin/final-questions")
async def add_final_questions(
    request: FinalQuestionsRequest, _: None = Depends(rate_limiter)
):
    """Add questions to a final assessment or a fixed assessment"""
    try:
        # Check if this is for a fixed assessment
        if request.assessment_id:
            # This is for a fixed assessment
            # First, validate the assessment exists and is a fixed assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                        (request.assessment_id,),
                    )
                    assessment = cur.fetchone()
                    if not assessment:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Assessment with ID {request.assessment_id} not found",
                        )

                    if assessment[1] != "fixed":
                        raise HTTPException(
                            status_code=400,
                            detail="Questions can only be assigned to assessments with 'fixed' question selection mode",
                        )

                    # Get the skill IDs associated with this assessment
                    cur.execute(
                        "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                        (request.assessment_id,),
                    )
                    results = cur.fetchall()
                    if not results:
                        raise HTTPException(
                            status_code=400,
                            detail="Assessment has no associated skills",
                        )

                    # Extract skill IDs from the results
                    skill_ids = []
                    for row in results:
                        skill_id = row[0]
                        skill_ids.append(skill_id)

                    # Check if all question IDs exist and belong to the assessment's skills
                    # Use parameterized queries for both question IDs and skill IDs
                    q_placeholders = ",".join(["%s"] * len(request.question_ids))
                    s_placeholders = ",".join(["%s"] * len(skill_ids))

                    query = f"""
                        SELECT COUNT(*)
                        FROM questions
                        WHERE que_id IN ({q_placeholders})
                        AND skill_id IN ({s_placeholders})
                    """
                    # Combine both lists of parameters
                    params = request.question_ids + skill_ids
                    cur.execute(query, params)
                    count = cur.fetchone()[0]
                    if count != len(request.question_ids):
                        raise HTTPException(
                            status_code=400,
                            detail="Some question IDs do not exist or are not associated with the assessment's skills",
                        )

                    # Check if the questions meet the minimum difficulty requirements
                    cur.execute(
                        f"""
                        SELECT level, COUNT(*)
                        FROM questions
                        WHERE que_id IN ({q_placeholders})
                        GROUP BY level
                        """,
                        request.question_ids,
                    )
                    level_counts = dict(cur.fetchall())

                    # Get minimum required counts from environment variables
                    min_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                    min_intermediate = int(
                        os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6")
                    )
                    min_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                    selected_easy = level_counts.get("easy", 0)
                    selected_intermediate = level_counts.get("intermediate", 0)
                    selected_advanced = level_counts.get("advanced", 0)

                    # Validate minimum requirements (allow more than minimum)
                    if (
                        selected_easy < min_easy
                        or selected_intermediate < min_intermediate
                        or selected_advanced < min_advanced
                    ):
                        raise HTTPException(
                            status_code=400,
                            detail=(
                                f"Selection must contain at least "
                                f"{min_easy} easy, {min_intermediate} intermediate, "
                                f"and {min_advanced} advanced questions (you can add more). Found: "
                                f"easy: {selected_easy}, intermediate: {selected_intermediate}, "
                                f"advanced: {selected_advanced}"
                            ),
                        )

                    # Clear existing assigned questions for this assessment
                    cur.execute(
                        "DELETE FROM assessment_questions WHERE assessment_id = %s",
                        (request.assessment_id,),
                    )

                    # Insert the new question assignments
                    for question_id in request.question_ids:
                        cur.execute(
                            """
                            INSERT INTO assessment_questions (assessment_id, question_id)
                            VALUES (%s, %s)
                            """,
                            (request.assessment_id, question_id),
                        )

                    conn.commit()

                    response_data = {
                        "assessment_id": request.assessment_id,
                        "count": len(request.question_ids),
                        "difficulty_counts": {
                            "easy": selected_easy,
                            "intermediate": selected_intermediate,
                            "advanced": selected_advanced,
                        },
                    }
                    hashed_data = hash_ids_in_response(response_data)
                    return success_response(
                        data=hashed_data,
                        message=(
                            f"Successfully assigned {len(request.question_ids)} questions to fixed assessment "
                            f"(minimum requirements: {min_easy} easy, {min_intermediate} intermediate, "
                            f"{min_advanced} advanced)"
                        ),
                    )
        else:
            # This is for a regular final assessment
            # Insert the final questions
            result = insert_final_questions_db(request.question_ids)

            if result:
                return success_response(
                    data={"count": len(request.question_ids)},
                    message="Final questions added successfully",
                )
            else:
                raise HTTPException(
                    status_code=400, detail="Failed to add final questions"
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error adding final questions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error adding final questions: {str(e)}"
        )


@app.get("/api/admin/sessions")
async def get_sessions(
    limit: int = Query(3, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[str] = Query(
        None, description="Filter by session status: 'pending', 'completed', or 'all'"
    ),
):
    """
    Get sessions with pagination and optional status filtering

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
        status_filter: Filter by session status ('pending', 'completed', or 'all')
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Build WHERE clause based on status filter
                where_clause = ""
                count_where_clause = ""

                if status_filter == "pending":
                    where_clause = "WHERE (s.status = 'pending' OR s.status = 'created' OR s.status IS NULL)"
                    count_where_clause = "WHERE (s.status = 'pending' OR s.status = 'created' OR s.status IS NULL)"
                elif status_filter == "completed":
                    where_clause = (
                        "WHERE (s.status = 'completed' OR s.status = 'finished')"
                    )
                    count_where_clause = (
                        "WHERE (s.status = 'completed' OR s.status = 'finished')"
                    )
                # If status_filter is 'all' or None, no WHERE clause needed

                # Get total count first
                count_query = f"""
                    SELECT COUNT(*)
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    JOIN users u ON s.user_id = u.id
                    {count_where_clause}
                """
                cur.execute(count_query)
                total = cur.fetchone()[0]

                # Get paginated results
                main_query = f"""
                    SELECT s.id, s.code as session_code, u.external_id as username,
                           s.assessment_id, s.score, s.status,
                           s.created_at, s.completed_at, s.started_at,
                           a.name as assessment_name,
                           CASE
                               WHEN a.question_selection_mode = 'fixed' THEN
                                   (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                               ELSE
                                   (SELECT COUNT(DISTINCT ua.question_id)
                                    FROM user_answers ua
                                    WHERE ua.session_id = s.id)
                           END as total_questions
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    JOIN users u ON s.user_id = u.id
                    {where_clause}
                    ORDER BY s.created_at DESC
                    LIMIT %s OFFSET %s
                """
                cur.execute(main_query, (limit, offset))
                sessions = [dict(row) for row in cur.fetchall()]

                # Transform response to include hashed IDs
                hashed_sessions = hash_ids_in_response(sessions)

                # Return paginated response
                return paginated_response(
                    data=hashed_sessions,
                    total=total,
                    limit=limit,
                    offset=offset,
                    message="Sessions retrieved successfully",
                    additional_data={"status_filter": status_filter or "all"},
                )
    except Exception as e:
        error(f"Error getting sessions: {str(e)}")
        return error_response(
            message=f"Error getting sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/user/{email}/sessions")
async def get_user_sessions_by_email(email: str):
    """Get sessions for a specific user identified by email"""
    try:

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, get the user's internal ID
                cur.execute(
                    """
                    SELECT id FROM users WHERE email = %s
                    """,
                    (email,),
                )
                user_result = cur.fetchone()
                if not user_result:
                    warning(f"User with email {email} not found in database")
                    return success_response(
                        data={"sessions": []},
                        message=f"No user found with email {email}",
                    )

                user_id = user_result["id"]

                # Now get all sessions for this user
                cur.execute(
                    """
                    SELECT s.id, s.code as session_code, u.external_id as username,
                           s.assessment_id, s.score, s.status,
                           s.created_at, s.completed_at, s.started_at,
                           a.name as assessment_name, a.question_selection_mode,
                           CASE
                               WHEN a.question_selection_mode = 'fixed' THEN
                                   (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                               ELSE
                                   (SELECT COUNT(DISTINCT ua.question_id)
                                    FROM user_answers ua
                                    WHERE ua.session_id = s.id)
                           END as total_questions
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    JOIN users u ON s.user_id = u.id
                    WHERE s.user_id = %s
                    ORDER BY s.created_at DESC
                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]
                hashed_sessions = hash_ids_in_response(sessions)

                return success_response(
                    data={
                        "sessions": hashed_sessions,
                        "total": len(sessions),
                        "count": len(sessions),
                    },
                    message="User sessions retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user sessions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting user sessions: {str(e)}"
        )


@app.post("/api/admin/sessions")
async def generate_sessions(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate session codes for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")
        usernames = request_data.get("usernames", "")

        # Validate inputs
        if not assessment_id:
            return error_response(
                message="Assessment ID is required",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        if not usernames:
            return error_response(
                message="Usernames are required",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        # Validate assessment_id is a valid integer
        try:
            assessment_id = int(assessment_id)
        except (ValueError, TypeError):
            return error_response(
                message="Assessment ID must be a valid number",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        # Split the comma-separated usernames and validate
        user_ids = [
            username.strip() for username in usernames.split(",") if username.strip()
        ]

        if not user_ids:
            return error_response(
                message="No valid usernames provided",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        # Validate username or email format
        invalid_usernames = []
        for username in user_ids:
            if len(username) < 2 or len(username) > 100:
                invalid_usernames.append(username)
            elif "@" in username:
                # Email validation
                import re

                if not re.match(
                    r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", username
                ):
                    invalid_usernames.append(username)
            # Check for invalid characters (basic alphanumeric + underscore + dash + dot) for non-email usernames
            elif not all(c.isalnum() or c in ["_", "-", "."] for c in username):
                invalid_usernames.append(username)

        if invalid_usernames:
            return error_response(
                message=(
                    f"Invalid entries: {', '.join(invalid_usernames)}. "
                    f"Usernames must be 2-50 characters with only letters, numbers, "
                    f"underscore, dash, or dot. Emails must be in valid format."
                ),
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        # Check for duplicate usernames
        if len(user_ids) != len(set(user_ids)):
            return error_response(
                message="Duplicate usernames are not allowed",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Validate assessment exists and get details
                cur.execute(
                    "SELECT id, name, is_final, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return error_response(
                        message=f"Assessment with ID {assessment_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                # For fixed assessments, check if questions are assigned
                if assessment["question_selection_mode"] == "fixed":
                    cur.execute(
                        "SELECT COUNT(*) FROM assessment_questions WHERE assessment_id = %s",
                        (assessment_id,),
                    )
                    question_count = cur.fetchone()[0]
                    if question_count == 0:
                        return error_response(
                            message="Cannot generate sessions for fixed assessment without assigned questions. "
                            "Please assign questions first.",
                            code=status.HTTP_400_BAD_REQUEST,
                            error_type="BadRequest",
                        )

                # Check if any of the users already have active sessions for this assessment
                existing_sessions = []
                for username in user_ids:
                    cur.execute(
                        """
                        SELECT s.code, u.external_id
                        FROM sessions s
                        JOIN users u ON s.user_id = u.id
                        WHERE u.external_id = %s AND s.assessment_id = %s AND s.status IN ('pending', 'in_progress')
                        """,
                        (username, assessment_id),
                    )
                    existing = cur.fetchone()
                    if existing:
                        existing_sessions.append(
                            f"{username} (code: {existing['code']})"
                        )

                if existing_sessions:
                    return error_response(
                        message=f"The following users already have active sessions for this assessment: "
                        f"{', '.join(existing_sessions)}",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )

                created_sessions = []
                failed_sessions = []

                for user_id_external in user_ids:
                    try:
                        user_internal_id = get_or_create_user(user_id_external)

                        # Generate a unique 6-digit session code with retry limit
                        session_code = ""
                        max_attempts = 10
                        attempts = 0

                        while attempts < max_attempts:
                            session_code = str(random.randint(100000, 999999)).zfill(6)
                            cur.execute(
                                "SELECT id FROM sessions WHERE code = %s",
                                (session_code,),
                            )
                            if not cur.fetchone():
                                break  # Unique code found
                            attempts += 1

                        if attempts >= max_attempts:
                            failed_sessions.append(
                                f"{user_id_external} (could not generate unique session code)"
                            )
                            continue

                        cur.execute(
                            """INSERT INTO sessions
                               (code, user_id, assessment_id, status, created_at)
                               VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
                               RETURNING id""",
                            (
                                session_code,
                                user_internal_id,
                                assessment_id,
                                "pending",
                            ),
                        )
                        session_db_id = cur.fetchone()["id"]
                        created_sessions.append(
                            {
                                "id": session_db_id,
                                "username": user_id_external,
                                "sessionCode": session_code,
                                "sessionDbId": session_db_id,
                            }
                        )
                    except Exception as user_error:
                        error(
                            f"Error creating session for user {user_id_external}: {str(user_error)}"
                        )
                        failed_sessions.append(
                            f"{user_id_external} (error: {str(user_error)})"
                        )

                conn.commit()

                # Prepare response
                response = {
                    "message": f"Generated {len(created_sessions)} session codes",
                    "sessions": created_sessions,
                }

                if failed_sessions:
                    response["warnings"] = (
                        f"Failed to create sessions for: {', '.join(failed_sessions)}"
                    )

                if not created_sessions:
                    return error_response(
                        message=f"Failed to create any sessions. Errors: {', '.join(failed_sessions)}",
                        code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        error_type="InternalServerError",
                    )

                hashed_data = hash_ids_in_response(response)
                return success_response(
                    data=hashed_data,
                    message=f"Generated {len(created_sessions)} session codes",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating sessions: {str(e)}", exc_info=True)
        return error_response(
            message=f"Unexpected error generating sessions: {str(e)}. "
            f"Please try again or contact support if the problem persists.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


def validate_assessment_skills(assessment_id: int, required_skill_ids: List[int]):
    """
    Validate that an assessment has all required skill associations.
    """
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            # Get the skill_id for this assessment
            cur.execute(
                "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                (assessment_id,),
            )
            result = cur.fetchone()
            if not result or not result[0]:
                return False

            # Parse the JSON array of skill IDs
            assessment_skill_ids = json.loads(result[0])

            # Check if all required skill IDs are in the assessment's skill IDs
            return all(
                skill_id in assessment_skill_ids for skill_id in required_skill_ids
            )


@app.post("/api/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on user or topic"""
    try:
        if request.report_type == "user_wise" and request.user_name:
            base_report, score_report = assessment_report_by_user(
                request.user_name, request.quiz_type or "mock"
            )
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated user-wise report for {request.user_name}",
            )

        elif request.report_type == "topic_wise" and request.report_topic:
            base_report, score_report = assessment_report_by_topic(
                request.report_topic, request.quiz_type or "mock"
            )
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated topic-wise report for {request.report_topic}",
            )

        elif request.report_type == "assessment_wise" and request.assessment_base_name:
            result = assessment_report_with_question_stats(
                request.assessment_base_name, request.quiz_type or "mock"
            )
            return success_response(
                data={
                    "base_report": result["base_report"],
                    "score_report": result["score_report"],
                },
                message=f"Generated assessment-wise report for {request.assessment_base_name}",
            )

        else:
            return error_response(
                message="Invalid report type or missing required parameters",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    except Exception as e:
        error(f"Error generating report: {str(e)}")
        return error_response(
            message=f"Error generating report: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/assessments")
async def get_assessments(
    limit: int = Query(7, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get all assessments with pagination

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total count first
                cur.execute("SELECT COUNT(*) FROM assessments")
                total = cur.fetchone()[0]

                # Get paginated results
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset),
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Get skill IDs for each assessment
                for assessment in assessments:
                    cur.execute(
                        """
                        SELECT skill_id FROM assessment_skills
                        WHERE assessment_id = %s
                        """,
                        (assessment["id"],),
                    )
                    skill_id_rows = cur.fetchall()

                    # Extract all skill IDs into a list
                    skill_ids = []
                    for row in skill_id_rows:
                        skill_id = row[0]
                        # Check if it's a JSON string (unlikely based on schema, but being cautious)
                        if (
                            isinstance(skill_id, str)
                            and skill_id.startswith("[")
                            and skill_id.endswith("]")
                        ):
                            try:
                                json_skill_ids = json.loads(skill_id)
                                skill_ids.extend(json_skill_ids)
                            except json.JSONDecodeError:
                                skill_ids.append(skill_id)
                        else:
                            # It's a single skill ID (integer)
                            skill_ids.append(skill_id)

                    assessment["skill_ids"] = skill_ids

                # Transform response to include hashed IDs
                hashed_assessments = hash_ids_in_response(assessments)

                # Return paginated response
                return paginated_response(
                    data=hashed_assessments,
                    total=total,
                    limit=limit,
                    offset=offset,
                    message="Assessments retrieved successfully",
                )
    except Exception as e:
        error(f"Error fetching assessments: {str(e)}")
        return error_response(
            message=f"Error fetching assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/assessment/{assessment_id}")
async def get_assessment(assessment_id: str, include_questions: bool = Query(False)):
    """Get a single assessment with optional questions inclusion"""
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = assessment_id
        if not assessment_id.isdigit():
            decoded_id = decode_assessment_id(assessment_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                return error_response(
                    message=f"Invalid assessment ID format: {assessment_id}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )
        else:
            actual_id = int(assessment_id)

        # Use the new function from questions_logging
        assessment_dict = get_assessment_by_id(actual_id, include_questions=include_questions)

        if not assessment_dict:
            return error_response(
                message=f"Assessment with ID {actual_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Transform response to include hashed IDs
        hashed_assessment = hash_ids_in_response(assessment_dict)

        return success_response(
            data=hashed_assessment,
            message="Assessment retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/skill-questions/{skill_id}")
async def get_skill_questions(skill_id: str):
    """Get questions for a specific skill"""
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = skill_id
        if not skill_id.isdigit():
            decoded_id = decode_skill_id(skill_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                return error_response(
                    message=f"Invalid skill ID format: {skill_id}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )
        else:
            actual_id = int(skill_id)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the skill exists
                cur.execute("SELECT id, name FROM skills WHERE id = %s", (actual_id,))
                skill = cur.fetchone()
                if not skill:
                    return error_response(
                        message=f"Skill with ID {skill_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                # Get all questions for this skill
                cur.execute(
                    """
                    SELECT que_id, topic, level, question, options, answer, time
                    FROM questions
                    WHERE skill_id = %s
                    ORDER BY level, time DESC
                """,
                    (actual_id,),
                )

                questions = [dict(row) for row in cur.fetchall()]

                # Return empty questions array instead of 404 when skill exists but has no questions
                response_data = {
                    "skill_id": actual_id,
                    "skill_name": skill["name"],
                    "questions": questions,
                }

                # Transform response to include hashed IDs
                hashed_data = hash_ids_in_response(response_data)

                return success_response(
                    data=hashed_data,
                    message=f"Retrieved {len(questions)} questions for skill '{skill['name']}'",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for skill {skill_id}: {str(e)}")
        return error_response(
            message=f"Error fetching questions for skill: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/assessment-questions/{assessment_id}")
async def get_assessment_questions(assessment_id: int):
    """Get all available questions for an assessment based on its associated skills"""
    try:
        # Use the new function from questions_logging
        result = get_assessment_questions_by_id(assessment_id)

        if result is None:
            return error_response(
                message=f"Assessment with ID {assessment_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        return success_response(
            data=result,
            message="Assessment questions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching questions for assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/fixed-assessment-questions/{assessment_id}")
async def get_fixed_assessment_questions(assessment_id: int):
    """Get the assigned questions for a fixed assessment"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists and is a fixed assessment
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    return error_response(
                        message=f"Assessment with ID {assessment_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                if assessment["question_selection_mode"] != "fixed":
                    return error_response(
                        message=f"Assessment with ID {assessment_id} is not a fixed assessment",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )

                # Get the assigned questions for this assessment
                cur.execute(
                    "SELECT question_id FROM assessment_questions WHERE assessment_id = %s ORDER BY id",
                    (assessment_id,),
                )
                results = cur.fetchall()

                # Extract question IDs
                question_ids = [row[0] for row in results]

                response_data = {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_ids": question_ids,
                }

                return success_response(
                    data=response_data,
                    message=f"Retrieved {len(question_ids)} fixed questions for assessment '{assessment['name']}'",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(
            f"Error fetching fixed assessment questions for assessment {assessment_id}: {str(e)}"
        )
        return error_response(
            message=f"Error fetching fixed assessment questions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.post("/api/admin/generate-link")
async def generate_quiz_link(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate a shareable link for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")

        if not assessment_id:
            raise HTTPException(status_code=400, detail="Assessment ID is required")

        # Verify the assessment exists
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, name FROM assessments WHERE id = %s", (assessment_id,)
                )
                assessment = cur.fetchone()

                if not assessment:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Assessment with ID {assessment_id} not found",
                    )

        # Generate the quiz link using hashed session code
        # Use the frontend URL from environment variables
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:5173")

        # For a quiz link, we need a session code - this should be generated or retrieved
        # For now, create a sample session code (in real usage, this should come from a session)
        # You might want to generate a session code here or get it from the request
        session_code = "445583"  # This should be dynamically generated or retrieved

        # Hash the session code for the URL
        from .utils.hashid_utils import encode_session_code

        hashed_session_code = encode_session_code(session_code)

        quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"

        response_data = {
            "link": quiz_link,
            "assessment_id": assessment_id,
            "assessment_name": assessment["name"],
            "session_code": session_code,
            "hashed_session_code": hashed_session_code,
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(
            data=hashed_data, message="Quiz link generated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating quiz link: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating quiz link: {str(e)}"
        )


@app.get("/api/admin/assessments/{assessment_id}")
async def get_single_assessment(assessment_id: int):
    """Get a single assessment by ID for the quiz link page"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Assessment with ID {assessment_id} not found",
                    )

                return dict(assessment)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching assessment: {str(e)}"
        )


@app.get("/api/admin/sessions/{session_id}/details")
async def get_session_details(session_id: str):
    """Get detailed session information by session ID (numeric ID or 6-digit session code)"""
    try:
        info(
            f"Session detail request for ID: {session_id} (type: {type(session_id)}, length: {len(session_id)})"
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                session_dict = None

                # Check if it's a 6-digit session code first (before general numeric check)
                if len(session_id) == 6 and session_id.isdigit():
                    info(f"Using 6-digit session code: {session_id}")

                    cur.execute(
                        """
                        SELECT s.id, s.code as session_code, u.external_id as username,
                               s.assessment_id, s.score, s.status,
                               s.created_at, s.completed_at, s.started_at,
                               a.name as assessment_name,
                               CASE
                                   WHEN a.question_selection_mode = 'fixed' THEN
                                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                                   ELSE
                                       (SELECT COUNT(DISTINCT ua.question_id)
                                        FROM user_answers ua
                                        WHERE ua.session_id = s.id)
                               END as total_questions
                        FROM sessions s
                        JOIN assessments a ON s.assessment_id = a.id
                        JOIN users u ON s.user_id = u.id
                        WHERE s.code = %s
                        """,
                        (session_id,),
                    )
                    session = cur.fetchone()
                    if session:
                        session_dict = dict(session)

                # If it's a numeric ID (but not 6 digits), get directly by ID
                elif session_id.isdigit():
                    actual_id = int(session_id)
                    info(f"Using numeric session ID: {actual_id}")

                    cur.execute(
                        """
                        SELECT s.id, s.code as session_code, u.external_id as username,
                               s.assessment_id, s.score, s.status,
                               s.created_at, s.completed_at, s.started_at,
                               a.name as assessment_name,
                               CASE
                                   WHEN a.question_selection_mode = 'fixed' THEN
                                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                                   ELSE
                                       (SELECT COUNT(DISTINCT ua.question_id)
                                        FROM user_answers ua
                                        WHERE ua.session_id = s.id)
                               END as total_questions
                        FROM sessions s
                        JOIN assessments a ON s.assessment_id = a.id
                        JOIN users u ON s.user_id = u.id
                        WHERE s.id = %s
                        """,
                        (actual_id,),
                    )
                    session = cur.fetchone()
                    if session:
                        session_dict = dict(session)

                else:
                    # The hash might be encoded as different types due to context detection issues
                    # Try to decode with all possible decoders
                    from .utils.hashid_utils import (
                        decode_assessment_id,
                        decode_session_code,
                        detect_hash_type,
                    )

                    info(f"Attempting to decode hash: {session_id}")
                    hash_type = detect_hash_type(session_id)
                    info(f"Detected hash type: {hash_type}")

                    decoded_id = None

                    # Try session decoder first
                    decoded_id = decode_session_code(session_id)
                    if decoded_id:
                        info(
                            f"Successfully decoded as session hash: {session_id} -> {decoded_id}"
                        )
                    else:
                        # If detected as assessment, try assessment decoder
                        # (the session might have been incorrectly encoded as assessment)
                        if hash_type == "assessment":
                            decoded_id = decode_assessment_id(session_id)
                            if decoded_id:
                                info(
                                    f"Successfully decoded as assessment hash (treating as session): "
                                    f"{session_id} -> {decoded_id}"
                                )

                    info(f"Final decode result: {decoded_id}")

                    if decoded_id:
                        info(
                            f"Successfully decoded {session_id} as session hash to ID: {decoded_id}"
                        )
                        # Use the decoded ID to get the session directly
                        cur.execute(
                            """
                            SELECT s.id, s.code as session_code, u.external_id as username,
                                   s.assessment_id, s.score, s.status,
                                   s.created_at, s.completed_at, s.started_at,
                                   a.name as assessment_name,
                                   CASE
                                       WHEN a.question_selection_mode = 'fixed' THEN
                                           (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                                       ELSE
                                           (SELECT COUNT(DISTINCT ua.question_id)
                                            FROM user_answers ua
                                            WHERE ua.session_id = s.id)
                                   END as total_questions
                            FROM sessions s
                            JOIN assessments a ON s.assessment_id = a.id
                            JOIN users u ON s.user_id = u.id
                            WHERE s.id = %s
                            """,
                            (decoded_id,),
                        )
                        session = cur.fetchone()
                        if session:
                            session_dict = dict(session)
                            # Convert datetime objects to ISO strings
                            for key, value in session_dict.items():
                                if hasattr(value, "isoformat"):
                                    session_dict[key] = value.isoformat()
                    else:
                        warning(f"Invalid session hash format: {session_id}")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid session hash: {session_id}",
                        )

                if not session_dict:
                    # Provide helpful error message based on session_id format
                    if len(session_id) == 6 and session_id.isdigit():
                        error_detail = (
                            f"Session with 6-digit code {session_id} not found"
                        )
                    elif session_id.isdigit():
                        error_detail = f"Session with numeric ID {session_id} not found"
                    else:
                        error_detail = (
                            f"Session with ID {session_id} not found. "
                            f"Expected: numeric ID or 6-digit session code"
                        )

                    warning(f"Session lookup failed: {error_detail}")
                    raise HTTPException(status_code=404, detail=error_detail)

                # Apply hash transformation to sessions found by numeric ID or 6-digit code
                if session_dict and "id_hash" not in session_dict:
                    # Add correct total possible score and performance level for completed sessions
                    if (
                        session_dict.get("status") == "completed"
                        and session_dict.get("score") is not None
                    ):
                        from .models.db_manager import (
                            calculate_total_score_for_assessment,
                            get_performance_level_with_correct_total,
                        )

                        obtained_score = float(session_dict["score"])
                        assessment_id = session_dict["assessment_id"]
                        session_internal_id = session_dict["id"]

                        # Calculate correct total possible score
                        total_possible_score = calculate_total_score_for_assessment(
                            assessment_id, session_internal_id
                        )

                        # Calculate performance level
                        performance_level = get_performance_level_with_correct_total(
                            obtained_score, assessment_id, session_internal_id
                        )

                        # Calculate percentage
                        percentage = (
                            (obtained_score / total_possible_score * 100)
                            if total_possible_score > 0
                            else 0
                        )

                        # Add to session dict
                        session_dict["obtained_score"] = obtained_score
                        session_dict["total_possible_score"] = total_possible_score
                        session_dict["percentage"] = round(percentage, 2)
                        session_dict["performance_level"] = performance_level

                    # Convert datetime objects to ISO strings before hash transformation
                    for key, value in session_dict.items():
                        if hasattr(value, "isoformat"):
                            session_dict[key] = value.isoformat()
                    session_dict = hash_ids_in_response(session_dict)

                info(
                    f"Returning session data keys: {list(session_dict.keys()) if session_dict else None}"
                )

                return success_response(
                    data=session_dict, message="Session details retrieved successfully"
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting session details for ID {session_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting session details: {str(e)}"
        )


@app.get("/api/admin/sessions/{session_code}/user")
async def get_session_user(session_code: str):
    """Get the username associated with a session code"""
    try:
        if not session_code or len(session_code) != 6 or not session_code.isdigit():
            raise HTTPException(
                status_code=400, detail="Session code must be a 6-digit number"
            )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, check if the session exists
                cur.execute(
                    """
                    SELECT s.id, s.user_id, s.assessment_id, s.status
                    FROM sessions s
                    WHERE s.code = %s
                    """,
                    (session_code,),
                )
                session_result = cur.fetchone()

                if not session_result:
                    warning(f"No session found for code: {session_code}")
                    raise HTTPException(
                        status_code=404, detail="Session code not found"
                    )

                # Now get the user details
                cur.execute(
                    """
                    SELECT u.external_id, u.display_name, u.email
                    FROM users u
                    WHERE u.id = %s
                    """,
                    (session_result["user_id"],),
                )
                user_result = cur.fetchone()

                if not user_result:
                    warning(f"No user found for user_id: {session_result['user_id']}")
                    raise HTTPException(
                        status_code=404, detail="User not found for this session"
                    )

                # Get the assessment details
                cur.execute(
                    """
                    SELECT a.name as assessment_name, a.is_final
                    FROM assessments a
                    WHERE a.id = %s
                    """,
                    (session_result["assessment_id"],),
                )
                assessment_result = cur.fetchone()

                if not assessment_result:
                    warning(
                        f"No assessment found for assessment_id: {session_result['assessment_id']}"
                    )
                    raise HTTPException(
                        status_code=404, detail="Assessment not found for this session"
                    )

                # Use display_name if available, otherwise fall back to external_id
                username = (
                    user_result["display_name"]
                    if user_result["display_name"]
                    else user_result["external_id"]
                )

                response_data = {
                    "username": username,
                    "assessment_id": session_result["assessment_id"],
                    "assessment_name": assessment_result["assessment_name"],
                    "is_final": assessment_result["is_final"],
                    "session_status": session_result["status"],
                }
                return hash_ids_in_response(response_data)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching session user for code {session_code}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching session user: {str(e)}"
        )


@app.get("/api/admin/assessments-with-sessions")
async def get_assessments_with_sessions():
    """Get only assessments that have existing sessions"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT DISTINCT a.id, a.name, a.description, a.is_final,
                           a.total_questions, a.question_selection_mode,
                           a.composition, a.created_at,
                           COUNT(s.id) as session_count
                    FROM assessments a
                    INNER JOIN sessions s ON a.id = s.assessment_id
                    GROUP BY a.id, a.name, a.description, a.is_final,
                             a.total_questions, a.question_selection_mode,
                             a.composition, a.created_at
                    ORDER BY a.created_at DESC
                    """
                )
                assessments = [dict(row) for row in cur.fetchall()]
                hashed_assessments = hash_ids_in_response(assessments)

                return success_response(
                    data={"assessments": hashed_assessments},
                    message="Assessments with sessions retrieved successfully",
                )

    except Exception as e:
        error(f"Error fetching assessments with sessions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching assessments with sessions: {str(e)}",
        )


@app.get("/api/admin/users/{user_id}/skill-performance")
async def get_user_skill_performance(user_id: int):
    """
    Get skill performance data for a specific user

    Returns skill-based metrics including:
    - Total questions answered per skill
    - Correct answers per skill
    - Total score per skill
    - Average score per skill
    - Accuracy percentage per skill
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,

                        -- Easy level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS easy_correct,
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS easy_incorrect,

                        -- Intermediate level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS intermediate_correct,
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS intermediate_incorrect,

                        -- Advanced level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS advanced_correct,
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS advanced_incorrect

                    FROM user_answers ua
                    JOIN sessions sess ON ua.session_id = sess.id
                    JOIN users u ON sess.user_id = u.id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )
                skill_performance = [dict(row) for row in cur.fetchall()]

                # Convert numeric values to appropriate types for JSON serialization
                for skill in skill_performance:
                    skill["total_questions_answered"] = int(
                        skill["total_questions_answered"]
                    )
                    skill["correct_answers"] = int(skill["correct_answers"])
                    skill["total_score"] = float(skill["total_score"])
                    skill["avg_score"] = float(skill["avg_score"])
                    skill["accuracy_percentage"] = float(skill["accuracy_percentage"])

                return success_response(
                    data=skill_performance,
                    message=f"Skill performance data retrieved successfully for user {user_id}",
                )

    except Exception as e:
        error(f"Error fetching skill performance for user {user_id}: {str(e)}")
        return error_response(
            message=f"Error fetching skill performance data: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/reports/skillwise-heatmap")
async def get_skillwise_heatmap():
    """
    Get skill performance data for all users in a format suitable for a heatmap

    Returns a matrix of user-skill performance data with accuracy percentages
    """
    heatmap_data = get_skillwise_heatmap_data()

    if heatmap_data is None:
        return error_response(
            message="Error fetching skillwise heatmap data",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )

    return success_response(
        data=heatmap_data,
        message="Skillwise heatmap data retrieved successfully",
    )


@app.get("/api/admin/users")
async def get_all_users():
    """Get all users from the database"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        u.id,
                        u.display_name,
                        u.created_at,
                        COUNT(DISTINCT s.id) as session_count
                    FROM users u
                    LEFT JOIN sessions s ON u.id = s.user_id
                    GROUP BY u.id, u.display_name, u.created_at
                    ORDER BY u.created_at DESC
                    """
                )
                users = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings and sanitize data
                for user in users:
                    if user["created_at"]:
                        user["created_at"] = user["created_at"].isoformat()
                    # Ensure display_name is not None
                    if not user["display_name"]:
                        user["display_name"] = f"User {user['id']}"

                return success_response(
                    data={"users": users},
                    message="Users retrieved successfully",
                )
    except Exception as e:
        error(f"Error getting users: {str(e)}")
        return error_response(
            message=f"Error getting users: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/admin/users/{user_id}/assessments")
async def get_user_assessments(user_id: int):
    """Get all assessments taken by a specific user with their scores"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (only safe information)
                cur.execute(
                    """
                    SELECT id, display_name
                    FROM users
                    WHERE id = %s
                    """,
                    (user_id,),
                )
                user = cur.fetchone()

                if not user:
                    return error_response(
                        message=f"User not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = f"User {user_dict['id']}"

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session[
                            "session_created"
                        ].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session[
                            "session_completed"
                        ].isoformat()

                response_data = {"user": user_dict, "assessments": sessions}
                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message="User assessments retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments: {str(e)}")
        return error_response(
            message=f"Error getting user assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/user/{email}/skill-performance")
async def get_user_skill_performance_by_email(email: str):
    """
    Get skill performance data for a specific user identified by email

    Returns skill-based metrics including:
    - Total questions answered per skill
    - Correct answers per skill
    - Total score per skill
    - Average score per skill
    - Accuracy percentage per skill
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, get the user's internal ID
                cur.execute(
                    """
                    SELECT id FROM users WHERE email = %s
                    """,
                    (email,),
                )
                user_result = cur.fetchone()
                if not user_result:
                    return error_response(
                        message=f"User with email {email} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )
                user_id = user_result["id"]

                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,

                        -- Easy level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS easy_correct,
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS easy_incorrect,

                        -- Intermediate level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS intermediate_correct,
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS intermediate_incorrect,

                        -- Advanced level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS advanced_correct,
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS advanced_incorrect

                    FROM user_answers ua
                    JOIN sessions sess ON ua.session_id = sess.id
                    JOIN users u ON sess.user_id = u.id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )

                skills = [dict(row) for row in cur.fetchall()]

                if not skills:
                    # Return a placeholder for no data
                    return success_response(
                        data={
                            "user_id": user_id,
                            "skills": [
                                {
                                    "skill_id": 0,
                                    "skill_name": "No Data Available",
                                    "total_questions_answered": 0,
                                    "correct_answers": 0,
                                    "total_score": 0,
                                    "average_score": 0,
                                    "accuracy_percentage": 0,
                                }
                            ],
                        },
                        message="No skill performance data available for this user",
                    )

                return success_response(
                    data={"user_id": user_id, "skills": skills},
                    message="User skill performance retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user skill performance: {str(e)}")
        return error_response(
            message="Error retrieving user skill performance",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@app.get("/api/user/{email}/assessments")
async def get_user_assessments_by_email(email: str):
    """Get all assessments taken by a specific user identified by email"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (only safe information)
                cur.execute(
                    """
                    SELECT id, display_name
                    FROM users
                    WHERE email = %s
                    """,
                    (email,),
                )
                user = cur.fetchone()

                if not user:
                    return error_response(
                        message=f"User not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = f"User {user_dict['id']}"

                user_id = user_dict["id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session[
                            "session_created"
                        ].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session[
                            "session_completed"
                        ].isoformat()

                response_data = {"user": user_dict, "assessments": sessions}
                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message="User assessments retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments: {str(e)}")
        return error_response(
            message="Error retrieving user assessments",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("SERVER_PORT", "8000"))
    uvicorn.run(app, host="0.0.0.0", port=port)
