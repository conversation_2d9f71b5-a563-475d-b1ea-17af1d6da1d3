/**
 * Centralized Authentication Service
 * Manages user authentication state and prevents duplicate API calls
 */

import { debug, error } from '@/utils/logger';

class AuthService {
  constructor() {
    this.userInfoCache = null;
    this.userInfoCacheTime = 0;
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    this.pendingUserInfoRequest = null; // Prevent concurrent requests
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!localStorage.getItem('access_token');
  }

  /**
   * Get user info from cache or localStorage
   */
  getUserInfo() {
    // First try cache
    if (this.userInfoCache && this.isCacheValid()) {
      return this.userInfoCache;
    }

    // Then try localStorage
    const storedUserInfo = localStorage.getItem('user_info');
    if (storedUserInfo) {
      try {
        const userInfo = JSON.parse(storedUserInfo);
        // Update cache
        this.userInfoCache = userInfo;
        this.userInfoCacheTime = Date.now();
        return userInfo;
      } catch (e) {
        error('Error parsing stored user info:', { error: e });
      }
    }

    return null;
  }

  /**
   * Check if cache is still valid
   */
  isCacheValid() {
    const now = Date.now();
    return this.userInfoCache && (now - this.userInfoCacheTime) < this.CACHE_DURATION;
  }

  /**
   * Fetch user info from backend with caching and deduplication
   */
  async fetchUserInfo(force = false) {
    // If not authenticated, return null
    if (!this.isAuthenticated()) {
      return null;
    }

    // If cache is valid and not forcing refresh, return cached data
    if (!force && this.isCacheValid()) {
      debug('Using cached user info');
      return this.userInfoCache;
    }

    // If there's already a pending request, wait for it
    if (this.pendingUserInfoRequest) {
      debug('Waiting for pending user info request');
      return await this.pendingUserInfoRequest;
    }

    // Create new request
    this.pendingUserInfoRequest = this._fetchUserInfoFromBackend();
    
    try {
      const userInfo = await this.pendingUserInfoRequest;
      return userInfo;
    } finally {
      // Clear pending request
      this.pendingUserInfoRequest = null;
    }
  }

  /**
   * Internal method to fetch user info from backend
   */
  async _fetchUserInfoFromBackend() {
    const token = localStorage.getItem('access_token');
    
    try {
      debug('Fetching fresh user info from backend');
      const response = await fetch('/auth/userinfo', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.authenticated && data.user) {
          // Update cache
          this.userInfoCache = data.user;
          this.userInfoCacheTime = Date.now();
          // Update localStorage
          localStorage.setItem('user_info', JSON.stringify(data.user));
          debug('User info fetched and cached');
          return data.user;
        }
      } else {
        error('Failed to fetch user info');
        return null;
      }
    } catch (fetchError) {
      error('Error fetching user info:', { error: fetchError });
      return null;
    }
  }

  /**
   * Clear cache and stored user info
   */
  clearUserInfo() {
    this.userInfoCache = null;
    this.userInfoCacheTime = 0;
    this.pendingUserInfoRequest = null;
    localStorage.removeItem('user_info');
    localStorage.removeItem('access_token');
    debug('User info cleared');
  }

  /**
   * Update user info in cache and localStorage
   */
  setUserInfo(userInfo) {
    this.userInfoCache = userInfo;
    this.userInfoCacheTime = Date.now();
    localStorage.setItem('user_info', JSON.stringify(userInfo));
    debug('User info updated');
  }

  /**
   * Check if user has admin role
   */
  isAdmin() {
    const userInfo = this.getUserInfo();
    if (!userInfo || !userInfo.groups) {
      return false;
    }
    
    const adminGroupName = import.meta.env.VITE_ADMIN_GROUP_NAME || 'admins';
    return userInfo.groups.includes(adminGroupName);
  }

  /**
   * Check if user has employee role
   */
  isEmployee() {
    const userInfo = this.getUserInfo();
    if (!userInfo || !userInfo.groups) {
      return false;
    }
    
    const employeeGroupName = import.meta.env.VITE_EMPLOYEE_GROUP_NAME || 'employees';
    return userInfo.groups.includes(employeeGroupName);
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
