<template>
  <div>
    <!-- Show admin home if user has admin group -->
    <Home v-if="showAdminHome" />

    <!-- Show user home if user has employee group -->
    <UserHome v-if="showUserHome" />

    <!-- Show message if user has no relevant groups -->
    <div v-if="!showAdminHome && !showUserHome" class="min-h-screen flex items-center justify-center">
      <div class="text-center p-8 bg-gray-100 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Access Restricted</h1>
        <p class="text-gray-600">You don't have permission to access this application.</p>
        <p class="text-gray-600 mt-2">Please contact your administrator for assistance.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { debug, info, warning, error } from '@/utils/logger';
import authService from '@/services/authService';
import Home from './Home.vue';
import UserHome from './UserHome.vue';

// Track which components to show
const showAdminHome = ref(false);
const showUserHome = ref(false);

onMounted(() => {
  // Determine which components to show based on user groups
  const userInfo = authService.getUserInfo();
  if (userInfo) {
    debug('User info in DynamicHome', { userInfo });

    if (userInfo.groups) {
      debug('User groups array', { groups: userInfo.groups });

      const hasAdminGroup = authService.isAdmin();
      const hasEmployeeGroup = authService.isEmployee();

      debug('User group membership', {
        hasAdminGroup,
        hasEmployeeGroup
      });

      // Prioritize admin view if user has admin group
      if (hasAdminGroup) {
        info('Admin user detected, showing admin home');
        showAdminHome.value = true;
        showUserHome.value = false; // Don't show both interfaces
      }
      // Only show user home if user has employee group but not admin group
      else if (hasEmployeeGroup) {
        info('Employee user detected, showing user home');
        showUserHome.value = true;
        showAdminHome.value = false;
      }

      // Log which components will be shown
      debug('Final component visibility state', {
        showAdminHome: showAdminHome.value,
        showUserHome: showUserHome.value
      });
    }
  } else {
    debug('No user info available');
  }
});
</script>
